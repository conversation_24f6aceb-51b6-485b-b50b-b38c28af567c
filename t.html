<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>海歌娱乐 - 即将上线 | High Game Entertainment - Coming Soon</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #000000; /* 背景改为黑色 */
            color: #333;
            overflow-x: hidden;
        }
        .container {
            text-align: center;
            padding: 20px;
            background-color: #000000; /* 容器保持白色以保证金色文字对比度 */
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            max-width: 90%;
            width: 100%;
            box-sizing: border-box;
        }
        h1 {
            font-size: 2.2em; /* 中文标题字体增大 */
            margin-bottom: 10px;
            line-height: 1.2;
            color: #FFD700; /* 金色 */
        }
        p {
            font-size: 1.1em;
            margin: 5px 0;
            line-height: 1.5;
            color: #FFD700; /* 金色 */
        }
        #countdown {
            font-size: 1.2em;
            margin-top: 15px;
            color: #e74c3c; /* 倒计时保持红色 */
            font-weight: bold;
        }

        /* 手机屏幕适配 */
        @media (max-width: 600px) {
            h1 {
                font-size: 1.7em; /* 手机上中文标题字体增大 */
            }
            p {
                font-size: 0.9em;
            }
            #countdown {
                font-size: 1em;
            }
            .container {
                padding: 15px;
                margin: 10px;
            }
        }

        /* 超小屏幕适配（如低端手机） */
        @media (max-width: 400px) {
            h1 {
                font-size: 1.4em; /* 超小屏幕中文标题字体增大 */
            }
            p {
                font-size: 0.8em;
            }
            #countdown {
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>海歌娱乐，即将上线</h1>
        <p>High Game Entertainment, Coming Soon</p>
        <div id="countdown">倒计时加载中...</div>
    </div>

    <script>
        // 设置上线时间（示例：2025年7月10日 00:00:00，柬埔寨时间，UTC+7）
        const launchDate = new Date('2025-07-10T00:00:00+07:00').getTime();

        // 更新倒计时
        function updateCountdown() {
            const now = new Date().getTime();
            const timeLeft = launchDate - now;

            if (timeLeft <= 0) {
                document.getElementById('countdown').innerHTML = '已上线！';
                return;
            }

            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

            document.getElementById('countdown').innerHTML = 
                `倒计时: ${days}天 ${hours}小时 ${minutes}分钟 ${seconds}秒`;
        }

        // 每秒更新倒计时
        setInterval(updateCountdown, 1000);
        updateCountdown(); // 立即运行一次
    </script>
</body>
</html>