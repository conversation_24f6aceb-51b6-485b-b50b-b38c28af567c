@charset "utf-8";

body {
	font-family:arial,helvetica,sans-serif;
	font-size: 14px;
	font-size-adjust:none;
	font-stretch:normal;
	font-style:normal;
	font-variant:normal;
	font-weight:normal;
	line-height:1.4;
	color: #121212;
	background-color:#383636;
	font-family: -apple-system,BlinkMacSystemFont,Helvetica Neue,PingFang SC,Microsoft YaHei,Source Han Sans SC,Noto Sans CJK SC,WenQuanYi Micro Hei,sans-serif;
}
body,ul,ol,dl,dd,h1,h2,h3,h4,h5,h6,p,form,fieldset,legend,input,textarea,select,button,th,td {
	margin:0;
	padding:0;
	
}
h1,h2,h3,h4,h5,h6 {
	font-size:100%;
	font-weight:normal;
}
table {
	font-size:inherit;
}
input,select {
	font-family:arial,helvetica,clean,sans-serif;
	font-size:100%;
	font-size-adjust:none;
	font-stretch:normal;
	font-style:normal;
	font-variant:normal;
	font-weight:normal;
	line-height:normal;
}
button {
	overflow:visible;
}
th,address,cite {
	font-style:normal;
	font-weight:normal;
}
li {
	list-style-image:none;
	list-style-position:outside;
	list-style-type:none;
	}
img,fieldset {
	border:0 none;
}
ins {
	text-decoration:none;
}
body a {
	color: #121212;
}
body a:hover {
	text-decoration:none;
}
button[disabled],html input[disabled] {
	cursor:default;
}

.row-20 {
	margin-left:-10px;
	margin-right:-10px;
}
.col-20 {
	padding-left:10px;
	padding-right:10px;
}
.mt-20 {
	margin-top:20px;
}
.mb-20 {
	margin-bottom:20px;
}
.br-10 {
	border-radius:10px;
}
.flex-main {
	flex:1
}
.container {
	padding:0;
}
.repulsion {
	display:flex;
	justify-content:space-between;
}
.overflow {
	overflow:hidden;
	*zoom:1;
}
.textoverflow {
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap;
}
.hidden {
	position:absolute;
	clip:rect(0,0,0,0);
}
.bg_con {
	background-color:#fff;
	border-radius:5px;
	margin-bottom:13px;
	box-shadow:0 0 4px 0 #ddd;
}
.con_right {
	width:300px;
	margin-left:26px;
}
.pagewrap {
	min-height:400px;
	max-width:1280px;
	margin:0 auto;
}
.fl {
	float:left;
}
.fr {
	float:right;
}
#app {
	width:1280px;
	min-height:calc(100vh - 323px);
	margin:0 auto;
}
/* 页眉 */
#header {
	font:14px/1.5 \5FAE\8F6F\96C5\9ED1,\5B8B\4F53,arial;
	height:auto;
	z-index:5;
	background-color:#fff;
}
#search {
	margin-top:18px;
}
#search-form {
	height:40px;
}
#search-form-input {
	float:left;
	width:400px;
	height:40px;
	line-height:20px;
	overflow:hidden;
	padding:7px 69px 7px 5px;
	background-color:#fafafa;
	border:solid 1px #eeeeee;
	border-right:0 none;
	font-size:14px;
	color:#333;
}
#search-form-submit {
	float:left;
	width:40px;
	height:40px;
	border:0 none;
	cursor:pointer;
	background:#03a4ed;
	color:#fff;
	font-size:14px;
}
#search-form-submit i {
	display:inline-block;
	width:20px;
	height:20px;
	background:url(../images/icon.png) -41px -37px no-repeat
}
.hot {
	max-width:440px;
	height:30px;
	line-height:30px;
	overflow:hidden;
}
.hot-item {
	margin-right:10px;
	padding-right:10px;
	color:#666;
}
.hot-item:hover {
	color:#0291d2;
}
#logo {
	display:block;
	height:80px;
	width:200px;
	overflow:hidden;
	left:0;
	top:0;
	position:relative;
	z-index:0;
}
/* 导航 */
.navWrap {
	position:relative;
	height:40px;
	background:#03a4ed;
	overflow:hidden;
}
#nav a {
	height:40px;
	line-height:40px;
	overflow:hidden;
	margin-right:1px;
	font-size:16px;
	color:#fff;
	display:inline-block;
	vertical-align:middle;
	padding:0 20px;
}
#nav a:hover {
	background:#0291d2;
	text-decoration:none;
	color:#fff;
}
#nav a.nav-cur,#nav a.nav-cur:hover {
	background:#0291d2;
}
/* 移动端查看全图 */
.light-box {
	position:fixed;
	top:0;
	z-index:999999999;
	width:100%;
	height:100%;
	background:rgba(0,0,0,.9)
}
.light-box>div {
	height:100%;
	width:100%;
	overflow:hidden;
	margin:0 auto;
}
.light-box ul,.light-box li {
	height:100%;
	width:100%;
}
.light-box .swiper-slide img {
	line-height:100%;
	max-width:100%;
	max-height:80%;
}
.light-box .swiper-slide {
	text-align:center;
	overflow:hidden;
	display:-webkit-box;
	-webkit-box-orient:vertical;
	-webkit-box-pack:center;
	-webkit-box-align:center;
}
.light-box .swiper-container-horizontal>.swiper-pagination {
	right:2rem!important;
	top:2rem!important;
	display:inline;
	width:auto;
	height:1.8rem;
	text-align:right;
	color:#c5c5c5;
	font-style:italic;
}
.light-box .swiper-pagination-current {
	font-size:1.8rem;
	color:#fff;
}
.light-box .close {
	display:block;
	width:30px;
	height:30px;
	overflow:hidden;
	position:absolute;
	left:1rem;
	top:2rem;
	font-size:0;
	z-index:99999;
	cursor:pointer;
}
.light-box .close:before,.light-box .close:after {
	content:'';
	position:absolute;
	left:1.2rem;
	top:.2rem;
	display:block;
	width:.13rem;
	height:1.8rem;
	background:#ccc;
	font-size:0;
	vertical-align:middle;
}
.light-box .close:before {
	-webkit-transform:rotate(45deg);
}
.light-box .close:after {
	-webkit-transform:rotate(-45deg);
}
/* 页脚 */
#footer {
	background-color:#fff;
	padding-top:40px;
	border-top:1px solid #f4f4f4;
	padding-bottom:40px;
	text-align:center;
	color:#999;
}
#footer a {
	font-size:14px;
	color:#999;
}
#footer .logosub {
	margin:0 auto;
	width:103px;
	height:35px;
	background:url(../images/wmz_btmicon_v2.png) no-repeat 0 0;
	background-size:100%;
}
.footerlink {
	margin-top:18px;
	margin-bottom:18px;
	line-height:24px;
}
.footerlink a {
	margin-right:15px;
	margin-left:15px;
	display:inline-block;
	white-space:nowrap
}
#footer .copyright {
	padding-left:50px;
	padding-right:50px;
}
#footer .copyright a,
#footer_v2 .copyright a  {
	margin-right:20px;
}
#footer .copyright a:last-child,
#footer_v2 .copyright a:last-child {
	margin-right:0;
}

#footer .gongan,
#footer_v2 .gongan {
	background:url(/static/wmzhe/images/footer_05.jpg) no-repeat 0px 0px;
	padding-left: 25px;
}

#footer .cnaac,
#footer_v2 .cnaac  {
	background:url(https://img.wmzhe.top/pics/f1/2f/f12f6c0116c014d5ca13fd6fcada845e.png) no-repeat 0px 0px;
	padding-left:24px;
}

#footer_v2 .gongan {
	display: none;
}
/* 面包屑 */
.crumbs {
	margin:20px 0;
	color:#797979;
	font-size:14px;
}
.crumbs_icon {
	float:left;
	margin-right:3px;
	width:16px;
	height:16px;
	background-image:url(https://img.wmzhe.top/pics/2020/4e/ff/vk2X5UF81TZxPbhSlhoEADZ0wkYaJ5qXdgn2bX0A.png);
	background-size:100%;
}
.crumbs a {
	font-size:14px;
	color:#797979;
	line-height:16px;
	margin-left:8px;
	margin-right:8px;
}
.crumbs span {
	margin-left:8px;
}
/* 侧边栏内容、导航样式 */
.right_silide_wrap {
	padding:20px 20px 38px;
	background-color:#fff;
	border-radius:5px;
	margin-bottom:12px;
}
.right_silide_wrap h3 a {
	line-height:20px;
}
.right_silide_wrap h3 {
	line-height:1.2;
}
.sidebar_header {
	margin-top:0;
	padding-top:0;
	margin-bottom:0;
	padding-bottom:20px;
	border-bottom:1px solid #ddd;
	position:relative;
	border-top:none;
}
.sidebar_header span {
	margin-left:18px;
	font-size:18px;
	font-weight:bold;
}
.sidebar_header a {
	color:#999;
	font-size:14px;
	font-weight:normal;
	float:right;
	line-height:20px;
}
.sidebar_header::before {
	position:absolute;
	top:1px;
	content:'';
	width:7px;
	height:18px;
	background-color:#03a4ed;
	border-radius:4px;
}
/* 文章标签 样式 */
.article_title_con span {
	position: relative;
}

.article_title_con span::after {
	position: absolute;
	content: "";
	width: 1px;
	height: 14px;
	background-color: #ddd;
	right: -30px;
	top: 50%;
	margin-top: -7px;
}

.article_title_con span:last-child:after {
	content: unset;
}

/* 文章通用板式 */
.aibyte_article {
	font-size:16px;
	line-height:32px;
}
.aibyte_article * {
	max-width:100%;
}
.aibyte_article h1,
.aibyte_article h2,
.aibyte_article h3,
.aibyte_article h4 {
	color:#333;
	font-weight: 600;
	margin: 19px 0;
}

.aibyte_article h1 {
	line-height:28px;
	font-size: 24px;
}

.aibyte_article h2 {
	line-height:24px;
	font-size: 18px;
}

.aibyte_article h3 {
	font-size:16px;
	line-height:20px;
}
.aibyte_article h4 {
	font-size: 16px;
	line-height:20px;
}
.aibyte_article ul,.aibyte_article ol,.aibyte_article p {
	margin:14px 0;
}
.aibyte_article .centerImg {
	display:flex;
	width:100%;
	align-items:center;
	justify-content:center;
}
.aibyte_article img,.aibyte_article .centerImg {
	margin:0 auto;
	width:auto !important;
	height:auto !important;
}
.aibyte_article .centerImg {
	background-color:#f7f8fa;
	margin:22px 0;
	display:flex;
}
.article_font blockquote {
	background:#F7F8FA;
	border-left:5px solid #ddd;
	padding:8px 20px;
	margin:22px 0;
}
.article_font pre {
	background:#F7F8FA;
	padding:22px 30px;
	margin:22px 0;
}
.aibyte_article blockquote+h3,.aibyte_article blockquote+h4,.aibyte_article h3+blockquote,.aibyte_article h4+blockquote,.aibyte_article pre+h3,.aibyte_article pre+h4,.aibyte_article h3+pre,.aibyte_article h4+pre,.aibyte_article .centerImg+h3,.aibyte_article .centerImg+h4,.aibyte_article h3+.centerImg,.aibyte_article h4+.centerImg {
	margin-top:27px;
}
.aibyte_article blockquote+.centerImg,.aibyte_article blockquote+pre,.aibyte_article .centerImg+blockquote,.aibyte_article blockquote+pre,.aibyte_article blockquote+.centerImg,.aibyte_article blockquote+pre {
	margin-top:30px;
}
.aibyte_article a {
	color:#027bc4;
}
.aibyte_article ol,.aibyte_article ul {
	padding-left:2em;
}
.aibyte_article ul li {
	list-style:disc;
}
.aibyte_article ol li {
	list-style:decimal;
}
.aibyte_article li + li {
	margin-top:10px;
}
/* 广告 */
.aibyte_ad {
	position:relative;
	background:#eee;
	overflow:hidden;
}
.aibyte_ad::after {
	content:"广告";
	position:absolute;
	bottom:5px;
	right:5px;
	background-color:rgba(0,0,0,.2);
	color:#fff;
	padding:2px 5px;
	border-radius:5px;
	font-size:12px;
	line-height:1.5em;
	z-index:10;
}
/* 软件首页跳转按钮 */
.goto_software {
	display:block;
	background-color:#07a5ec;
	height:88px;
	line-height:88px;
	font-size:18px;
	text-align:center;
	border-radius:5px;
	margin-bottom:10px;
	color:#fff;
	padding:32px 0 27px;
}
.goto_software i {
	display:inline-block;
	width:30px;
	height:30px;
	background-image:url(/static/wmzhe/images/article_software.png);
	background-size:100%;
	margin-right:15px;
	vertical-align:top;
}
.goto_software span {
	line-height:30px;
	display:inline-block;
	height:30px;
	vertical-align:top;
}
.cmbox {
	margin-top:10px;
	margin-bottom:10px;
	overflow:hidden;
	border:solid 1px #ebebeb;
	background:#fff;
}
.cmbox .title {
	padding:0;
	color:#000;
	line-height:42px;
	_overflow:hidden;
	border-bottom:solid 1px #ebebeb;
}
.cmbox .title h2 {
	font-size:16px;
	display:inline;
	font-weight:normal;
	padding:0 10px 0 10px;
	line-height:inherit;
}
.cmbox .title h2 i {
	display:inline-block;
	width:18px;
	height:18px;
	background:url(../images/icon.png) no-repeat -240px -42px;
	vertical-align:middle;
	margin-right:8px;
}
.cmbox .title p {
	float:left;
	width:225px;
	height:45px;
	line-height:45px;
	position:relative;
	font-size:14px;
}
.cmbox .title p {
	overflow:hidden;
	text-align:right;
}
.cmbox .title p a {
	padding:0 7px;
	border-right:1px solid #cfcfcf;
}
.cmbox .title .more {
	float:right;
	height:45px;
	line-height:45px;
	font-size:14px;
	width:60px;
}
.cmbox .title .tab {
	padding-left:20px;
	display:inline-block;
}
.cmbox .title .tab i {
	font-style:normal;
	height:42px;
	line-height:42px;
	color:#666;
	display:inline-block;
	padding:0 20px;
	text-align:center;
	cursor:pointer;
}
.cmbox .title .tab .cur {
	position:relative;
	border-bottom:solid 1px #09a6ed;
	color:#027bc4;
}
.cmbox .title .tab .cur:before {
	position:absolute;
	content:'';
	left:50%;
	margin-left:-5px;
	bottom:-5px;
	background:url(../images/icon.png) no-repeat -408px -90px;
	width:9px;
	height:5px;
}
.cmbox .content {
	padding-top:15px;
	display:none;
	position:relative;
}
.cmbox .content-on {
	display:flex;
}
.cmbox .content .extra {
	z-index:4;
	display:none;
	position:absolute;
	top:-42px;
	right:1em;
	height:42px;
	line-height:42px;
	overflow:hidden;
}
.cmbox .content-on .extra {
	display:block;
}
.right-side {
	float:right;
	width:260px;
}
.right-side .title {
	font-size:14px;
	color:#027bc4;
	height:34px;
	line-height:34px;
	border-bottom:solid 1px #ebebeb;
	font-weight:500;
}
.right-side i {
	display:inline-block;
	width:16px;
	height:16px;
	background:url(../images/icon.png);
	vertical-align:middle;
}
.right-side .title a {
	margin-left:20px;
	font-size:12px;
	color:#999999;
}
.right-side .titpye {
	float:left;
	margin:9.5px 8px;
	background-position:-272px -85px;
}
.right-side .title .tituij {
	background-position:-239px -83px
}
.right-side .titdown {
	float:left;
	margin:9px 8px;
	background-position:-304px -85px;
}
.right-side .list {
	width:100%;
	overflow:hidden;
	zoom:1;
	padding:0 10px 10px;
}
.right-side .list li {
	float:left;
	width:114px;
	margin-right:10px;
}
.right-side .list li a {
	display:block;
	margin-top:11px;
	height:32px;
	line-height:32px;
	color:#666666;
	background-color:#ebebeb;
	text-align:center;
}
.right-side .list li.current a,.right-side .list li a:hover {
	background-color:#03a4ed;
	color:#fff;
	text-decoration:none;
}

.wmz-tag-gray {
	border: 1px solid #ddd;
	padding: 5px 10px;
	border-radius: 8px;
	margin-left: 8px;
	line-height: 36px;
}

.wmz-tag-gray:hover {
	color: #333;
}

@media (min-width:1300px) {
	.container,.container-lg,.container-md,.container-sm,.container-xl {
		max-width:1280px;
	}
}
@media only screen and (max-width:768px) {
	body > .container {
		width:100%;
	}
	#header {
		height:3.5rem;
		position:relative;
		width:100% !important;
		background-color:#fff;
	}
	#logo {
		margin:0 10px;
		padding-top:5px;
		display:inline-block;
		float:none;
		width:auto;
		height:56px;
	}
	#logo img {
		width:auto;
		height:46px;
	}
	#search {
		position:initial;
		float:right;
		width:170px;
		height:34px;
		margin:11px 10px 11px 0;
		background:#f2f2f2;
		border-radius:3px;
	}
	#search .search-input {
		float:left;
	}
	#search .link-icon {
		float:right;
		width:30px;
		height:34px;
		vertical-align:top;
		cursor:pointer;
	}
	#search .link-icon .icon {
		background-repeat:no-repeat;
		background-image:url(../images/sprite-common-sef7c107ce9.png);
		background-position:0 -306px;
		width:16px;
		height:14px;
		margin:9px auto;
		display:block;
	}
	#search-form-input {
		width:115px;
		height:34px;
		padding:0 5px 0 10px;
		line-height:28px;
		background:transparent;
		border:none;
		outline:none;
	}
	.hot {
		display:none;
	}
	.navWrap {
		overflow-x:auto;
		height:46px;
		padding-left:12px;
	}
	#nav {
		width:744px !important;
	}
	#nav a {
		font-size:17px;
		line-height:46px;
		height:46px;
		padding:0 12px;
	}
	#footer {
		padding-top:30px;
	}
	#footer .container {
		width:100%;
	}
	#footer .logosub {
		width:89px;
		height:30px;
	}
	.footerlink a,#footer .copyright a {
		font-size:12px !important;
		margin-left:10px;
		margin-right:10px;
	}
	#footer .copyright {
		line-height:16px !important;
	}
	.crumbs {
		margin-bottom:10px;
		margin-top:10px;
	}
	.pagewrap {
		padding-left:.5rem;
		padding-right:.5rem;
	}
	.aibyte_article blockquote {
		position:relative;
	}
	.aibyte_article blockquote::before {
		content:'';
		position:absolute;
		top:-10px;
		left:-5px;
		right:0;
		border-top:10px solid #1ec356;
		border-radius:10px 10px 0 0;
	}
	.goto_software {
		height:45px;
		line-height:45px;
		padding:11px 0;
	}
	.goto_software i {
		margin-right:10px;
		width:22px;
		height:22px;
	}
	.goto_software span {
		height:22px;
		line-height:22px;
		letter-spacing:3px;
	}
	.aibyte_ad {
		width:100% !important;
	}
	#A01,#B01,#C01 {
		margin-top:0 !important;
	}
	#fast-nav {
		margin-bottom:10px;
		padding-left:10px;
		padding-right:10px;
	}
	.cmbox {
		margin-left:.5rem;
		margin-right:1.7rem;
		background:none;
		border:none;
	}
	.cmbox .title .tab {
		display:flex;
		padding:0;
	}
	.cmbox .title .tab i {
		flex:1;
		padding:0;
	}
	.corner .photo::after {
		width:66px;
		height:65px;
		left:11px !important;
		top:2px !important;
	}

	.article_title_con span::after {
		right: -12px;
	}
}
@media only screen and (max-width:576px) {
	#app {
		width:100%;
		margin-top: 50px;
		padding-top: 10px;
	}
}

.adv-close{
	position: absolute;
	width: 40px;
	height: 18px;
	text-align: center;
	line-height: 18px;
	color: #fff;
	font-size: 12px;
	border-radius: 11px;
	background: rgba(153,154,170,.6);
	top: 8px;
	right: 8px;
	cursor: pointer;
	z-index: 9999;
}

pre{margin:15px auto;font:14px/20px Menlo,Monaco,Consolas,"Andale Mono","lucida console","Courier New", monospace;font-weight:300;white-space:pre-wrap;word-break:break-all;word-wrap:break-word;background-color:#f5f5f5;padding:10px 25px;line-height: 2em}
code{margin: auto 2px;padding: 4px;font-size: 13px;color: #333;border: 1px solid #ddd;background: #f6f6f6;border-radius: 2px}
.marked{padding:0.2em;margin:0;background-color: #eceae6;border-radius:3px;font-weight:bold;font-family:"SFMono-Regular",Consolas,"Liberation Mono",Menlo,Courier,monospace}
table.reference,table.tecspec{border-collapse:collapse;width:100%;margin-bottom:4px;margin-top:4px}
table.reference .fa {font-size:24px;}
table.reference tr:nth-child(odd){background-color:#f6f4f0}
table.reference tr:nth-child(even){background-color:#fff}
table.reference tr.fixzebra{background-color:#f6f4f0}
table.reference th{color:#fff;background-color:#555;border:1px solid #555;font-size:14px;padding-left:10px;vertical-align:top;text-align:left;line-height: 2em}
table.reference th a:link,table.reference th a:visited{color:#fff}
table.reference th a:active,table.reference th a:hover{color:#ee872a}
tr td:first-child{min-width:25px}
table.reference td{line-height:2em;min-width:24px;border:1px solid #d4d4d4;padding:5px;padding-top:7px;padding-bottom:7px;vertical-align:top}
table.reference td.example_code{vertical-align:bottom}
