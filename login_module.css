.login_module_wrap {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .6);
    top: 0;
    z-index: 20;
    display: none;
}

.login_module_wrap .cont {
    width: 600px;
    height: 450px;
    background-color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -230px;
    margin-left: -300px;
    border-radius: 10px;
    padding: 60px 80px 0 100px;
}


.login_module_wrap .logo {
    width: 200px;
    height: 80px;
      background-size: 100%;
    margin: 0 auto;
}

.login_module_wrap .line {
    width: 100%;
    height: 20px;
    position: relative;
    margin-top: 30px;
    text-align: center;
}

.line::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 1px;
    background-color: #ddd;
    left: 0;
    top: 10px;
}

.line span {
    position: absolute;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: #999;
    background-color: #fff;
    left: 50%;
    margin-left: -70px;
    padding-left: 10px;
    padding-right: 10px;
}

.login_module_wrap .line b {
    color: #5E81A3;
}

.login_module_wrap .login {
    margin-top: 50px;
    text-align: center;
    display: block;
}

.login_module_wrap  .login i {
    display: inline-block;
    width: 80px;
    height: 80px;
    background-image: url(/static/wmzhe/images/weixin.svg);
    background-size: 65%;
    background-repeat: no-repeat;
    background-position: center center;
    vertical-align: middle;
    margin-right: 5px;
    font-size: 30px;
    border: 2px solid #03b201;
    border-radius: 50%;
    margin-bottom: 20px;
}

.login_module_wrap .login p {
    color: #999;
}

.login_module_wrap .login_close {
    position: absolute;
    width: 30px;
    height: 30px;
    background-color: rgba(0, 0, 0, .4);
    border-radius: 50%;
    text-align: center;
    right: 10px;
    top: 10px;
}

.login_module_wrap .login_close i {
    display: inline-block;
    width: 16px;
    height: 16px;
        background-size: 100%;
    background-repeat: no-repeat;
    background-position: center center;
    margin-top: 7px;
}