<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 10px;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 10px;
            box-sizing: border-box;
        }
        h1 {
            font-size: 1.5em;
            text-align: center;
            margin-bottom: 15px;
        }
        label {
            font-size: 0.9em;
            display: block;
            margin-bottom: 5px;
        }
        input, button, select {
            margin: 5px 0;
            padding: 10px;
            width: 100%;
            box-sizing: border-box;
            font-size: 1em;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .highlight-base { 
            color: #006400; /* 深绿色 */
            font-weight: bold; 
        }
        .highlight-diff { 
            color: red; /* 加粗红色用于 arr */
            font-weight: bold; 
        }
        .highlight-n { 
            color: #1976d2; /* 加粗深绿色用于 N */
            font-weight: bold; 
        }
        @media (max-width: 1200px) {
            h1 { font-size: 1.2em; }
            input, button, select { padding: 8px; }
            .container { padding: 5px; }
            label { font-size: 0.85em; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>老苏开心查</h1>
        <label for="number">输入一个数字(必填项):</label>
        <input type="number" id="number" required>

        <label for="baseStep">区间步进单位:</label>
        <select id="baseStep">
            <option value="100">100 步进</option>
            <option value="1000" selected>1000 步进 (默认)</option>
            <option value="10000">10000 步进</option>
        </select>

        <label for="step">步进（默认0.005）:</label>
        <input type="number" id="step" step="0.001" value="0.005">

        <label for="left">左值（默认7.000）:</label>
        <input type="number" id="left" step="0.001" value="7.000">

        <label for="right">右值（默认7.900）:</label>
        <input type="number" id="right" step="0.001" value="7.900">

        <button onclick="calculate()">点我让老苏同志帮你猜一猜</button>
        <div class="result" id="result"></div>
    </div>

    <script>
        function calculate() {
            const startTime = performance.now();

            const baseNumberInput = document.getElementById('number');
            const baseNumber = parseFloat(baseNumberInput.value);
            if (!baseNumberInput.value || isNaN(baseNumber)) {
                document.getElementById('result').textContent = '请输入一个有效的数字！';
                return;
            }

            const baseStr = baseNumber.toString();
            const baseClass = (baseStr[baseStr.length - 1] === '0' || baseStr[baseStr.length - 1] === '5') ? 'highlight-base' : '';

            const step = parseFloat(document.getElementById('step').value) || 0.005;
            const startN = parseFloat(document.getElementById('left').value) || 7.200;
            const endN = parseFloat(document.getElementById('right').value) || 7.900;
            const baseStep = parseInt(document.getElementById('baseStep').value);

            let minValue = Math.floor((baseNumber * startN) / baseStep) * baseStep;
            let maxValue = Math.ceil((baseNumber * endN) / baseStep) * baseStep;
            let arrs = [];
            for (let i = minValue; i <= maxValue; i += baseStep) {
                arrs.push(i);
            }

            let results = [];
            let N = startN;
            while (N <= endN) {
                let Y = baseNumber * N;
                for (let arr of arrs) {
                    let diff = Math.abs(Y - arr);
                    if (diff < N && Y < arr) {
                        let result = { N, Y, arr, diff };
                        // 修改高亮逻辑：N 在 7.08 到 7.48 范围内，且满足 arr 是 10000 或 (N < 25000 时 1000) 的倍数
                        result.arrClass = (N >= 7.18 && N <= 7.48 && (arr % 10000 === 0 || (N < 25000 && arr % 1000 === 0))) ? 'highlight-diff' : '';
                        const nStr = N.toFixed(3);
                        result.nClass = (nStr[nStr.length - 1] === '0' || nStr[nStr.length - 1] === '5') ? 'highlight-n' : '';
                        results.push(result);
                    }
                }
                N = parseFloat((N + step).toFixed(3));
            }

            let resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '';
            if (results.length > 0) {
                resultDiv.innerHTML = `<p>区间步进单位: ${baseStep}</p>` +
                    results.map(r => `<p>(<span class="${baseClass}">${baseNumber}</span> * <span class="${r.nClass}">${r.N.toFixed(3)}</span> = ${r.Y.toFixed(2)}) - <span class="${r.arrClass}">${r.arr}</span> = ${r.diff.toFixed(2)} 小于 <span class="${r.nClass}">${r.N.toFixed(3)}</span></p>`).join('');
            } else {
                resultDiv.innerHTML = `<p>区间步进单位: ${baseStep}</p><p>没有找到符合条件的结果。</p>`;
            }

            const endTime = performance.now();
            resultDiv.innerHTML += `<p>总共耗时: ${(endTime - startTime).toFixed(2)} 毫秒</p>`;
        }
    </script>
</body>
</html>