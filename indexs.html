﻿<!DOCTYPE html>
<html lang="zh-cn"
  style="font-size: 16px; --status-bar-height: 0px; --top-window-height: 80px; --window-left: 128px; --window-right: 1152px; --window-margin: 128px; --tab-bar-height: 50px; --window-top: calc(0px + env(safe-area-inset-top)); --window-bottom: calc(50px + env(safe-area-inset-bottom));">



  <body class="game-d4" data-v-d35f2a3c="">
    <div id="app" data-v-app=""><uni-app class="uni-app--showtabbar" style="max-width:1024px;margin:0 auto;"><uni-layout
          class="uni-app--showtopwindow uni-app--showleftwindow uni-app--showrightwindow"><uni-top-window>
            <div class="uni-top-window" style="height: 92px; left: 0px; right: 0px;"><uni-view data-v-c3e5a1de=""
                class="top-window" navigation-bar-title-text="" matchtopwindow="true"
                showtopwindow="true" matchleftwindow="true" showleftwindow="true" matchrightwindow="true"
                showrightwindow="true"><uni-view data-v-884e8d98="" data-v-c3e5a1de="" class="top-bar global"><uni-view
                    data-v-884e8d98="" class="top-bar-content"><uni-view data-v-884e8d98="" class="left"><uni-navigator
                        data-v-884e8d98="" class="title"><a class="navigator-wrap" data-savepage-href="/"
                          href="https://www.boyi999.com/">
                          <div data-v-af6deea6="" class="van-image item-img" webp=""><img class="van-image__img"
                            style="object-fit: cover;"
                            src="logos.png"                      lazy="loaded" style="object-fit: fill;"><!----><!----></div>
                        </a></uni-navigator><uni-view data-v-0b3750b9="" data-v-884e8d98="" class="game-nav">
                              </uni-image><!----><uni-view
                    data-v-c3e5a1de="" class="right"><!----></uni-view></uni-view><!----><!----></uni-view></div>
            
          </uni-top-window>
          
          <uni-content><uni-main><uni-page data-page="pages/d4/Home/index"
                type="tabBar"><!----><uni-page-wrapper><uni-page-body data-v-d35f2a3c=""><uni-view data-v-d35f2a3c=""
                      style="display: none;"></uni-view><uni-view data-v-d35f2a3c="" class="container" style=""><uni-view
                        data-v-d35f2a3c="" class="fake-status-bar"><!----></uni-view><!----><!----></a></uni-navigator></uni-view></uni-view></uni-view></uni-view>
                        
                        <uni-view
                        data-v-b660c215="" data-v-d35f2a3c="" class="banner"></uni-view>
                              
                              <uni-view
                        data-v-d35f2a3c="" class="content">
                        <div data-v-eeb7f5ce="" data-v-d35f2a3c="" role="alert" class="van-notice-bar notice"><!---->
                          <div role="marquee" class="van-notice-bar__wrap">
                            
                          </div><!---->
                        </div><uni-view data-v-f07d8703="" data-v-7034d807="" data-v-d35f2a3c=""
                          class="card control season-count"><!----><uni-view data-v-f07d8703="" class="body"
                            style="padding-top: 0.375rem; padding-bottom: 0.375rem;"><uni-view data-v-7034d807=""
                              class="season-count-content"><uni-view
                                data-v-7034d807="" class="count-text"><uni-view data-v-11f99eae=""
                                  data-v-7034d807="" class="uni-countdown"><!----><!----><uni-text
                                    data-v-11f99eae="" class="uni-countdown__number"
                                    style="color: rgb(233, 201, 100); font-size: 14px; border-radius: 3px;"><span>
                                      环球已经恢复至以前原来一样牌桌！！
                                      祝各位老板蛇年好运！新游戏即将上线，敬请期待！</span>
                                  </uni-view></uni-view></uni-view><uni-navigator data-v-7034d807=""
                              class="helltides"><a class="navigator-wrap" data-savepage-href="/d4/helltides"
                                href="https://www.boyi999.com/">即时新闻</a></uni-navigator><uni-view
                              data-v-7034d807="" class="mask"><!----></uni-view></uni-view></uni-view>
                              
                              
                              
                              
                              
                                          
                                          
                                          
                                          
                                          <uni-view
                              data-v-d35f2a3c="" class="guide-panel"><uni-view data-v-d35f2a3c="" class="panel-item"><uni-view
                                  data-v-d35f2a3c="" class="title-row"><uni-view data-v-6f33070d="" data-v-d35f2a3c=""
                                    class="section-title" style="margin-top: 12px;"><uni-text
                                      data-v-6f33070d=""><span>相关服务</span></uni-text></uni-view></uni-view>
                                      
                                      
                                      <uni-view data-v-af6deea6="" data-v-d35f2a3c="" class="guide-list"><uni-view data-v-f07d8703=""
                                    data-v-af6deea6="" class="card control clickable guide-item control"><!----><uni-view
                                      data-v-f07d8703="" class="body" style="padding: 0px;"><uni-navigator data-v-af6deea6=""
                                        class=""><a class="navigator-wrap"
                                          data-savepage-href="/d4/guide?id=f5835fa6678e5c0804ff3c1f001f7f67"
                                          href="https://www.boyi999.com/"><uni-view
                                            data-v-af6deea6="" class="inner">
                                            <div data-v-af6deea6="" class="van-image item-img" webp=""><img class="van-image__img"
                                                style="object-fit: cover;"
                                                src="./tm.png" lazy="loaded"><!----><!----></div><uni-view data-v-af6deea6=""
                                              class="content"><uni-view data-v-af6deea6="" class="title">
                                                <div data-v-af6deea6="" class="van-image blz-icon" webp=""><img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./w.png" lazy="loaded"><!----><!----></div><uni-text
                                                  data-v-af6deea6=""><span>Talkmore全球在线沟通</span><uni-text
                                                  data-v-11f99eae="" class="uni-countdown__number"
                                                  style="color: rgb(233, 201, 100); font-size: 12px; border-radius: 3px;"><span>&nbsp;&nbsp;&nbsp;&nbsp; Talkmore聊天，以安全、快捷、加密为导向，上级代理可以在紧急情况下逐层联系再上
                                                    一级代理帮你暂封号，同时兼具担保功能、朋友圈打赏并兑现功能，请贵宾注册并加上级代理好友。 </span>
                                                
                                                
                                                
                                                
                                                </uni-text>
                                                  
                                              </uni-view><uni-view data-v-af6deea6="" class="bottom"><uni-view data-v-af6deea6=""
                                                  class="author">Boyi Entertainment</uni-view><uni-view data-v-af6deea6=""
                                                  class="date"><uni-text data-v-af6deea6=""><span>12天前</span></uni-text><uni-text data-v-7c2f6cb0="" data-v-af6deea6=""
                                                    class="uni-icons uniui-chatbubble-filled mgl-16"
                                                    style="font-size: 14px;"><span></span></uni-text><uni-text data-v-af6deea6=""
                                                    style="margin-left: 4px;"><span>20</span></uni-text></uni-view></uni-view></uni-view>
                                          </uni-view></a></uni-navigator></uni-view></uni-view>
  
  
                                          
                                          
                                          
                                          
                                          
                                          <uni-view data-v-f07d8703=""
                                    data-v-af6deea6="" class="card control clickable guide-item control"><!----><uni-view
                                      data-v-f07d8703="" class="body" style="padding: 0px;"><uni-navigator data-v-af6deea6=""
                                        class=""><a class="navigator-wrap"
                                          data-savepage-href="/d4/guide?id=c69c6e4c678e56b0018b6e5718c908e8"
                                          href="https://www.boyi999.com/"><uni-view
                                            data-v-af6deea6="" class="inner">
                                            <div data-v-af6deea6="" class="van-image item-img" webp=""><img class="van-image__img"
                                                style="object-fit: cover;"
                                                data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                src="./gb.png" lazy="loaded"><!----><!----></div><uni-view data-v-af6deea6=""
                                              class="content"><uni-view data-v-af6deea6="" class="title">
                                                <div data-v-af6deea6="" class="van-image blz-icon" webp=""><img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./w.png" lazy="loaded"><!----><!----></div><uni-text
                                                  data-v-af6deea6=""><span>皇牌贵宾会</span></uni-text>
  
                                                  <uni-text
                                                  data-v-11f99eae="" class="uni-countdown__number"
                                                  style="color: rgb(225, 19, 40); font-size: 12px; border-radius: 3px;"><span>&nbsp;&nbsp;&nbsp;&nbsp; 皇冠百家乐全网最公正平台！正式上线！手机登录请横向屏幕。</span>
                                                
                                                
                                                
                                                
                                                </uni-text>
  
  
                                              </uni-view><uni-view data-v-af6deea6="" class="bottom"><uni-view data-v-af6deea6=""
                                                  class="author">Boyi Entertainment</uni-view><uni-view data-v-af6deea6=""
                                                  class="date"><uni-text data-v-af6deea6=""><span>12天前</span></uni-text><uni-text data-v-7c2f6cb0="" data-v-af6deea6=""
                                                    class="uni-icons uniui-chatbubble-filled mgl-16"
                                                    style="font-size: 14px;"><span></span></uni-text><uni-text data-v-af6deea6=""
                                                    style="margin-left: 4px;"><span>4</span></uni-text></uni-view></uni-view></uni-view>
                                          </uni-view></a></uni-navigator></uni-view></uni-view><uni-view data-v-f07d8703=""
                                    data-v-af6deea6="" class="card control clickable guide-item control"><!----><uni-view
                                      data-v-f07d8703="" class="body" style="padding: 0px;"><uni-navigator data-v-af6deea6=""
                                        class=""><a class="navigator-wrap"
                                          data-savepage-href="/d4/guide?id=02b3d02c6780c208010d49bc77adc67e"
                                          href="https://www.boyi999.com/"><uni-view
                                            data-v-af6deea6="" class="inner">
                                            <div data-v-af6deea6="" class="van-image item-img" webp=""><img class="van-image__img"
                                                style="object-fit: cover;"
                                                data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                src="./logo_ob.png" lazy="loaded"><!----><!----></div><uni-view data-v-af6deea6=""
                                              class="content"><uni-view data-v-af6deea6="" class="title">
                                                <div data-v-af6deea6="" class="van-image blz-icon" webp=""><img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./w.png" lazy="loaded"><!----><!----></div><uni-text
                                                  data-v-af6deea6=""><span>欧博网址</span></uni-text>
  
  
                                                  <uni-text
                                                  data-v-11f99eae="" class="uni-countdown__number"
                                                  style="color: rgb(218, 218, 218); font-size: 12px; border-radius: 3px;"><span>&nbsp;&nbsp;&nbsp;&nbsp; 欧博频繁出现打庄出货机器投注等问题，管理网延时6003，可以转至皇牌继续游戏，请联系您的上级代理开管理网</span>
                                                
                                                
                                                
                                                
                                                </uni-text>
  
                                              </uni-view><uni-view data-v-af6deea6="" class="bottom"><uni-view data-v-af6deea6=""
                                                  class="author">Boyi Entertainment</uni-view><uni-view data-v-af6deea6=""
                                                  class="date"><uni-text data-v-af6deea6=""><span>23天前</span></uni-text><uni-text data-v-7c2f6cb0="" data-v-af6deea6=""
                                                    class="uni-icons uniui-chatbubble-filled mgl-16"
                                                    style="font-size: 14px;"><span></span></uni-text><uni-text data-v-af6deea6=""
                                                    style="margin-left: 4px;"><span>46</span></uni-text></uni-view></uni-view></uni-view>
                                          </uni-view></a></uni-navigator></uni-view></uni-view><uni-view data-v-f07d8703=""
                                    data-v-af6deea6="" class="card control clickable guide-item control"><!----><uni-view
                                      data-v-f07d8703="" class="body" style="padding: 0px;"><uni-navigator data-v-af6deea6=""
                                        class=""><a class="navigator-wrap"
                                          data-savepage-href="/d4/guide?id=59af7b52675c6c0402bd65396fde0365"
                                          href="https://www.boyi999.com/"><uni-view
                                            data-v-af6deea6="" class="inner">
                                            <div data-v-af6deea6="" class="van-image item-img" webp=""><img class="van-image__img"
                                                style="object-fit: cover;"
                                                data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                src="./logo_hg.png" lazy="loaded"><!----><!----></div><uni-view data-v-af6deea6=""
                                              class="content"><uni-view data-v-af6deea6="" class="title">
                                                <div data-v-af6deea6="" class="van-image blz-icon" webp=""><img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./w.png" lazy="loaded"><!----><!----></div><uni-text
                                                  data-v-af6deea6=""><span>国际体育皇冠网址</span></uni-text>
  
                                                  <uni-text
                                                  data-v-11f99eae="" class="uni-countdown__number"
                                                  style="color: rgb(218, 218, 218); font-size: 12px; border-radius: 3px;"><span>&nbsp;&nbsp;&nbsp;&nbsp; 全球体育娱乐平台，涵盖各大赛事，欢迎使用</span>
                                                
                                                
                                                
                                                
                                                </uni-text>
  
                                              </uni-view><uni-view data-v-af6deea6="" class="bottom"><uni-view data-v-af6deea6=""
                                                  class="author">Boyi Entertainment</uni-view><uni-view data-v-af6deea6=""
                                                  class="date"><uni-text data-v-af6deea6=""><span>1月前</span></uni-text><uni-text data-v-7c2f6cb0="" data-v-af6deea6=""
                                                    class="uni-icons uniui-chatbubble-filled mgl-16"
                                                    style="font-size: 14px;"><span></span></uni-text><uni-text data-v-af6deea6=""
                                                    style="margin-left: 4px;"><span>54</span></uni-text></uni-view></uni-view></uni-view>
                                          </uni-view></a></uni-navigator></uni-view></uni-view><uni-view data-v-f07d8703=""
                                    data-v-af6deea6="" class="card control clickable guide-item control"><!----><uni-view
                                      data-v-f07d8703="" class="body" style="padding: 0px;"><uni-navigator data-v-af6deea6=""
                                        class=""><a class="navigator-wrap"
                                          data-savepage-href="/d4/guide?id=cc3a41796748992d00dc1e6c01cb0b95"
                                          href="https://www.boyi999.com/"><uni-view
                                            data-v-af6deea6="" class="inner">
                                            <div data-v-af6deea6="" class="van-image item-img" webp=""><img class="van-image__img"
                                                style="object-fit: cover;"
                                                data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                src="./logo.3f6e1494.webp" lazy="loaded"><!----><!----></div>
          
                                            <uni-view data-v-af6deea6="" class="content"><uni-view data-v-af6deea6=""
                                                class="title">
                                                <div data-v-af6deea6="" class="van-image blz-icon" webp=""><img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./w.png" lazy="loaded"><!----><!----></div><uni-text
                                                  data-v-af6deea6=""><span>环球360</span></uni-text>
                                                  
                                                  <uni-text
                                                  data-v-11f99eae="" class="uni-countdown__number"
                                                  style="color: rgb(233, 201, 100); font-size: 12px; border-radius: 3px;"><span>&nbsp;&nbsp;&nbsp;&nbsp; 环球360维护结束，请正常登录</span>
                                                
                                                
                                                
                                                
                                                </uni-text>
                                                
                                              </uni-view><uni-view data-v-af6deea6="" class="bottom"><uni-view data-v-af6deea6=""
                                                  class="author">Boyi Entertainment</uni-view><uni-view data-v-af6deea6=""
                                                  class="date"><uni-text data-v-af6deea6=""><span>2月前</span></uni-text><uni-text data-v-7c2f6cb0="" data-v-af6deea6=""
                                                    class="uni-icons uniui-chatbubble-filled mgl-16"
                                                    style="font-size: 14px;"><span></span></uni-text><uni-text data-v-af6deea6=""
                                                    style="margin-left: 4px;"><span>30</span></uni-text></uni-view></uni-view></uni-view>
                                          </uni-view></a></uni-navigator></uni-view></uni-view>
                                  
                                    <uni-view data-v-f07d8703=""
                                    data-v-af6deea6="" class="card control clickable guide-item control"><!----><uni-view
                                      data-v-f07d8703="" class="body" style="padding: 0px;"><uni-navigator data-v-af6deea6=""
                                        class=""><a class="navigator-wrap"
                                          data-savepage-href="/d4/guide?id=f5835fa6678e5c0804ff3c1f001f7f67"
                                          href="https://www.boyi999.com/"><uni-view
                                            data-v-af6deea6="" class="inner">
                                            <div data-v-af6deea6="" class="van-image item-img" webp=""><img class="van-image__img"
                                                style="object-fit: cover;"
                                                data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                src="./logo_kk.png" lazy="loaded"><!----><!----></div><uni-view data-v-af6deea6=""
                                              class="content"><uni-view data-v-af6deea6="" class="title">
                                                <div data-v-af6deea6="" class="van-image blz-icon" webp=""><img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./w.png" lazy="loaded"><!----><!----></div><uni-text
                                                  data-v-af6deea6=""><span>卡卡湾电投</span></uni-text>
  
                                                  <uni-text
                                                  data-v-11f99eae="" class="uni-countdown__number"
                                                  style="color: rgb(225, 19, 40); font-size: 12px; border-radius: 3px;"><span>&nbsp;&nbsp;&nbsp;&nbsp; 有两位打百家乐99%胜率大神在丰益卡卡湾v7台v8台不定时带路打</span>
                                                
                                                
                                                
                                                
                                                </uni-text>
                                              </uni-view><uni-view data-v-af6deea6="" class="bottom"><uni-view data-v-af6deea6=""
                                                  class="author">Boyi Entertainment</uni-view><uni-view data-v-af6deea6=""
                                                  class="date"><uni-text data-v-af6deea6=""><span>12天前</span></uni-text><uni-text data-v-7c2f6cb0="" data-v-af6deea6=""
                                                    class="uni-icons uniui-chatbubble-filled mgl-16"
                                                    style="font-size: 14px;"><span></span></uni-text><uni-text data-v-af6deea6=""
                                                    style="margin-left: 4px;"><span>20</span></uni-text></uni-view></uni-view></uni-view>
                                          </uni-view></a></uni-navigator></uni-view></uni-view>
  
  
  
                                          <uni-view data-v-f07d8703=""
                                    data-v-af6deea6="" class="card control clickable guide-item control"><!----><uni-view
                                      data-v-f07d8703="" class="body" style="padding: 0px;"><uni-navigator data-v-af6deea6=""
                                        class=""><a class="navigator-wrap"
                                          data-savepage-href="/d4/guide?id=f5835fa6678e5c0804ff3c1f001f7f67"
                                          href="https://www.boyi999.com/"><uni-view
                                            data-v-af6deea6="" class="inner">
                                            <div data-v-af6deea6="" class="van-image item-img" webp=""><img class="van-image__img"
                                                style="object-fit: cover;"
                                                data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                src="./yx.png" lazy="loaded"><!----><!----></div><uni-view data-v-af6deea6=""
                                              class="content"><uni-view data-v-af6deea6="" class="title">
                                                <div data-v-af6deea6="" class="van-image blz-icon" webp=""><img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./w.png" lazy="loaded"><!----><!----></div><uni-text
                                                  data-v-af6deea6=""><span>亚星娱乐</span></uni-text>
  
                                                  <uni-text
                                                  data-v-11f99eae="" class="uni-countdown__number"
                                                  style="color: rgb(218, 218, 218); font-size: 12px; border-radius: 3px;"><span>&nbsp;&nbsp;&nbsp;&nbsp; 老牌平台，欢迎使用</span>
                                                
                                                
                                                
                                                
                                                </uni-text>
  
                                              </uni-view><uni-view data-v-af6deea6="" class="bottom"><uni-view data-v-af6deea6=""
                                                  class="author">Boyi Entertainment</uni-view><uni-view data-v-af6deea6=""
                                                  class="date"><uni-text data-v-af6deea6=""><span>12天前</span></uni-text><uni-text data-v-7c2f6cb0="" data-v-af6deea6=""
                                                    class="uni-icons uniui-chatbubble-filled mgl-16"
                                                    style="font-size: 14px;"><span></span></uni-text><uni-text data-v-af6deea6=""
                                                    style="margin-left: 4px;"><span>20</span></uni-text></uni-view></uni-view></uni-view>
                                          </uni-view></a></uni-navigator></uni-view></uni-view>
  
  
                                          <uni-view data-v-f07d8703=""
                                    data-v-af6deea6="" class="card control clickable guide-item control"><!----><uni-view
                                      data-v-f07d8703="" class="body" style="padding: 0px;"><uni-navigator data-v-af6deea6=""
                                        class=""><a class="navigator-wrap"
                                          data-savepage-href="/d4/guide?id=f5835fa6678e5c0804ff3c1f001f7f67"
                                          href="https://www.boyi999.com/"><uni-view
                                            data-v-af6deea6="" class="inner">
                                            <div data-v-af6deea6="" class="van-image item-img" webp=""><img class="van-image__img"
                                                style="object-fit: cover;"
                                                data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/cloudbase-cms/upload/2025-01-20/b95zz0r9a4nbji886qz7s4mjc19zb3cr_.webp?imageView2/2/ignore-error/1/w/1000/format/webp/q/80"
                                                src="./wl.png" lazy="loaded"><!----><!----></div><uni-view data-v-af6deea6=""
                                              class="content"><uni-view data-v-af6deea6="" class="title">
                                                <div data-v-af6deea6="" class="van-image blz-icon" webp=""><img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./w.png" lazy="loaded"><!----><!----></div><uni-text
                                                  data-v-af6deea6=""><span>万利平台</span></uni-text>
  
                                                  <uni-text
                                                  data-v-11f99eae="" class="uni-countdown__number"
                                                  style="color: rgb(218, 218, 218); font-size: 12px; border-radius: 3px;"><span>&nbsp;&nbsp;&nbsp;&nbsp; 万利会员/管理登录</span>
                                                
                                                
                                                
                                                
                                                </uni-text>
  
  
                                              </uni-view><uni-view data-v-af6deea6="" class="bottom"><uni-view data-v-af6deea6=""
                                                  class="author">Boyi Entertainment</uni-view><uni-view data-v-af6deea6=""
                                                  class="date"><uni-text data-v-af6deea6=""><span>12天前</span></uni-text><uni-text data-v-7c2f6cb0="" data-v-af6deea6=""
                                                    class="uni-icons uniui-chatbubble-filled mgl-16"
                                                    style="font-size: 14px;"><span></span></uni-text><uni-text data-v-af6deea6=""
                                                    style="margin-left: 4px;"><span>20</span></uni-text></uni-view></uni-view></uni-view>
                                          </uni-view></a></uni-navigator></uni-view></uni-view>
                                  
                                  
                                  </uni-view></uni-view></uni-view>
  
  
                                          
                                        
                                          <uni-view data-v-6f33070d="" data-v-d35f2a3c="" class="section-title"><uni-text
                                            data-v-6f33070d=""><span>游戏分类</span></uni-text></uni-view><uni-view data-v-a597d84a=""
                                          data-v-d35f2a3c="" class="tools-wrapper"><uni-view data-v-a597d84a="" class="tools"><uni-view
                                              data-v-f07d8703="" data-v-aa482c06="" data-v-a597d84a=""
                                              class="card control clickable image-card-wrapper"><!----><uni-view data-v-f07d8703=""
                                                class="body" style="padding: 0px;"><a data-v-aa482c06="" class="card-link" target=""
                                                  data-savepage-href="/d4/guide?id=c69c6e4c678e56b0018b6e5718c908e8"
                                                  href="https://www.boyi999.com/"><uni-view> <img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./g1.jpg" lazy="loaded"><uni-view
                                                      data-v-aa482c06="" class="content"><uni-view
                                                        data-v-a597d84a="">传统游戏</uni-view><!----></uni-view><uni-view data-v-aa482c06=""
                                                      class="mask"><!----></uni-view></uni-view></a></uni-view></uni-view><uni-view
                                              data-v-f07d8703="" data-v-aa482c06="" data-v-a597d84a=""
                                              class="card control clickable image-card-wrapper"><!----><uni-view data-v-f07d8703=""
                                                class="body" style="padding: 0px;"><a data-v-aa482c06="" class="card-link" target=""
                                                  data-savepage-href="/d4/guide?id=f5835fa6678e5c0804ff3c1f001f7f67"
                                                  href="https://www.boyi999.com/"><uni-view> <img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./g2.jpg" lazy="loaded"><uni-view
                                                      data-v-aa482c06="" class="content"><uni-view
                                                        data-v-a597d84a="">棋牌类游戏</uni-view><!----></uni-view><uni-view data-v-aa482c06=""
                                                      class="mask"><!----></uni-view></uni-view></a></uni-view></uni-view><uni-view
                                              data-v-f07d8703="" data-v-aa482c06="" data-v-a597d84a=""
                                              class="card control clickable image-card-wrapper"><!----><uni-view data-v-f07d8703=""
                                                class="body" style="padding: 0px;"><a data-v-aa482c06="" class="card-link" target=""
                                                  data-savepage-href="/d4/data/witchPower"
                                                  href="https://www.boyi999.com/"><uni-view> <img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./g3.jpg" lazy="loaded"><uni-view
                                                      data-v-aa482c06="" class="content"><uni-view
                                                        data-v-a597d84a="">德州精英</uni-view><!----></uni-view><uni-view data-v-aa482c06=""
                                                      class="mask"><!----></uni-view></uni-view></a></uni-view></uni-view><uni-view
                                              data-v-f07d8703="" data-v-aa482c06="" data-v-a597d84a=""
                                              class="card control clickable image-card-wrapper"><!----><uni-view data-v-f07d8703=""
                                                class="body" style="padding: 0px;"><a data-v-aa482c06="" class="card-link" target=""
                                                  data-savepage-href="/d4/guide?id=880c3388664a2a9b01318b7e3d44ff11"
                                                  href="https://www.boyi999.com/"><uni-view> <img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./g4.jpg" lazy="loaded"><uni-view
                                                      data-v-aa482c06="" class="content"><uni-view
                                                        data-v-a597d84a="">香港赛马</uni-view><!----></uni-view><uni-view data-v-aa482c06=""
                                                      class="mask"><!----></uni-view></uni-view></a></uni-view></uni-view></uni-view></uni-view><uni-view
                                          data-v-a597d84a="" data-v-d35f2a3c="" class="tools-wrapper"><uni-view data-v-a597d84a=""
                                            class="tools"><uni-view data-v-f07d8703="" data-v-aa482c06="" data-v-a597d84a=""
                                              class="card control clickable image-card-wrapper"><!----><uni-view data-v-f07d8703=""
                                                class="body" style="padding: 0px;"><a data-v-aa482c06="" class="card-link" target=""
                                                  data-savepage-href="/d4/planner" href="https://www.boyi999.com/"><uni-view> <img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./g5.png" lazy="loaded"><uni-view
                                                      data-v-aa482c06="" class="content"><uni-view
                                                        data-v-a597d84a="">老虎机开大奖</uni-view><!----></uni-view><uni-view data-v-aa482c06=""
                                                      class="mask"><!----></uni-view></uni-view></a></uni-view></uni-view><uni-view
                                              data-v-f07d8703="" data-v-aa482c06="" data-v-a597d84a=""
                                              class="card control clickable image-card-wrapper"><!----><uni-view data-v-f07d8703=""
                                                class="body" style="padding: 0px;"><a data-v-aa482c06="" class="card-link" target=""
                                                  data-savepage-href="/d4/builds" href="https://www.boyi999.com/"><uni-view> <img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./g6.jpg" lazy="loaded"><uni-view
                                                      data-v-aa482c06="" class="content"><uni-view
                                                        data-v-a597d84a="">在线彩票24小时服务</uni-view><!----></uni-view><uni-view data-v-aa482c06=""
                                                      class="mask"><!----></uni-view></uni-view></a></uni-view></uni-view><uni-view
                                              data-v-f07d8703="" data-v-aa482c06="" data-v-a597d84a=""
                                              class="card control clickable image-card-wrapper"><!----><uni-view data-v-f07d8703=""
                                                class="body" style="padding: 0px;"><a data-v-aa482c06="" class="card-link" target="_blank"
                                                  data-savepage-href="/d4-map/index.html"
                                                  href="https://www.boyi999.com/"><uni-view> <img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./g7.jpg" lazy="loaded"><uni-view
                                                      data-v-aa482c06="" class="content"><uni-view
                                                        data-v-a597d84a="">赛车类游戏</uni-view><!----></uni-view><uni-view data-v-aa482c06=""
                                                      class="mask"><!----></uni-view></uni-view></a></uni-view></uni-view><uni-view
                                              data-v-f07d8703="" data-v-aa482c06="" data-v-a597d84a=""
                                              class="card control clickable image-card-wrapper"><!----><uni-view data-v-f07d8703=""
                                                class="body" style="padding: 0px;"><a data-v-aa482c06="" class="card-link" target=""
                                                  data-savepage-href="/bbs" href="https://www.boyi999.com/"><uni-view> <img
                                                    class="van-image__img" style="object-fit: contain;"
                                                    data-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    data-savepage-src="https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/blizz-icon-small.svg"
                                                    src="./g8.jpg" lazy="loaded"><uni-view
                                                      data-v-aa482c06="" class="content"><uni-view
                                                        data-v-a597d84a="">捕鱼游戏精彩刺激</uni-view><!----></uni-view><uni-view data-v-aa482c06=""
                                                      class="mask"><!----></uni-view></uni-view></a></uni-view></uni-view></uni-view></uni-view>
  
  
                                        <uni-view data-v-63d441c7="" data-v-d35f2a3c="" class="page-footer"><uni-view
                                          data-v-63d441c7="" class="footer-content"><uni-view data-v-63d441c7=""
                                            class="link">博义娱乐 2025</uni-view></uni-view>
              
                
                <!---->
              </div>
            </div>
            <div class="uni-tabbar__item">
              
                
                
              </div>
            </div>
          </div>
          
        </uni-tabbar></uni-app></div>
    <!-- 公众号 JSSDK -->







<head>
 
  <style data-savepage-href="/assets/uni.d30a2008.css">
    uni-canvas {
      width: 300px;
      height: 150px;
      display: block;
      position: relative
    }

    uni-canvas>.uni-canvas-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%
    }

    uni-image {
      width: 320px;
      height: 240px;
      display: inline-block;
      overflow: hidden;
      position: relative
    }

    uni-image[hidden] {
      display: none
    }

    uni-image>div {
      width: 100%;
      height: 100%;
      background-repeat: no-repeat
    }

    uni-image>img {
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      user-select: none;
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0
    }

    uni-image>.uni-image-will-change {
      will-change: transform
    }

    uni-input {
      display: block;
      font-size: 16px;
      line-height: 1.4em;
      height: 1.4em;
      min-height: 1.4em;
      overflow: hidden
    }

    uni-input[hidden] {
      display: none
    }

    .uni-input-wrapper,
    .uni-input-placeholder,
    .uni-input-form,
    .uni-input-input {
      outline: none;
      border: none;
      padding: 0;
      margin: 0;
      text-decoration: inherit
    }

    .uni-input-wrapper,
    .uni-input-form {
      display: flex;
      position: relative;
      width: 100%;
      height: 100%;
      flex-direction: column;
      justify-content: center
    }

    .uni-input-placeholder,
    .uni-input-input {
      width: 100%
    }

    .uni-input-placeholder {
      position: absolute;
      top: auto !important;
      left: 0;
      color: gray;
      overflow: hidden;
      text-overflow: clip;
      white-space: pre;
      word-break: keep-all;
      pointer-events: none;
      line-height: inherit
    }

    .uni-input-input {
      position: relative;
      display: block;
      height: 100%;
      background: none;
      color: inherit;
      opacity: 1;
      font: inherit;
      line-height: inherit;
      letter-spacing: inherit;
      text-align: inherit;
      text-indent: inherit;
      text-transform: inherit;
      text-shadow: inherit
    }

    .uni-input-input[type=search]::-webkit-search-cancel-button,
    .uni-input-input[type=search]::-webkit-search-decoration {
      display: none
    }

    .uni-input-input::-webkit-outer-spin-button,
    .uni-input-input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      appearance: none;
      margin: 0
    }

    .uni-input-input[type=number] {
      -moz-appearance: textfield
    }

    .uni-input-input:disabled {
      -webkit-text-fill-color: currentcolor
    }

    uni-navigator {
      height: auto;
      width: auto;
      display: block;
      cursor: pointer
    }

    uni-navigator[hidden] {
      display: none
    }

    .navigator-hover {
      background-color: rgba(0, 0, 0, .1);
      opacity: .7
    }

    .navigator-wrap,
    .navigator-wrap:link,
    .navigator-wrap:visited,
    .navigator-wrap:hover,
    .navigator-wrap:active {
      text-decoration: none;
      color: inherit;
      cursor: pointer
    }

    .uni-scroll-view-refresher {
      position: relative;
      overflow: hidden;
      flex-shrink: 0
    }

    .uni-scroll-view-refresher-container {
      position: absolute;
      width: 100%;
      bottom: 0;
      display: flex;
      flex-direction: column-reverse
    }

    .uni-scroll-view-refresh {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center
    }

    .uni-scroll-view-refresh-inner {
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 0;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #fff;
      box-shadow: 0 1px 6px rgba(0, 0, 0, .118), 0 1px 4px rgba(0, 0, 0, .118)
    }

    .uni-scroll-view-refresh__spinner {
      transform-origin: center center;
      animation: uni-scroll-view-refresh-rotate 2s linear infinite
    }

    .uni-scroll-view-refresh__spinner>circle {
      stroke: currentColor;
      stroke-linecap: round;
      animation: uni-scroll-view-refresh-dash 2s linear infinite
    }

    @keyframes uni-scroll-view-refresh-rotate {
      0% {
        transform: rotate(0)
      }

      to {
        transform: rotate(360deg)
      }
    }

    @keyframes uni-scroll-view-refresh-dash {
      0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0
      }

      50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35px
      }

      to {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -124px
      }
    }

    @keyframes once-show {
      0% {
        top: 0
      }
    }

    uni-resize-sensor,
    uni-resize-sensor>div {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      overflow: hidden
    }

    uni-resize-sensor {
      display: block;
      z-index: -1;
      visibility: hidden;
      animation: once-show 1ms
    }

    uni-resize-sensor>div>div {
      position: absolute;
      left: 0;
      top: 0
    }

    uni-resize-sensor>div:first-child>div {
      width: 100000px;
      height: 100000px
    }

    uni-resize-sensor>div:last-child>div {
      width: 200%;
      height: 200%
    }

    uni-scroll-view {
      display: block;
      width: 100%
    }

    uni-scroll-view[hidden] {
      display: none
    }

    .uni-scroll-view {
      position: relative;
      -webkit-overflow-scrolling: touch;
      width: 100%;
      height: 100%;
      max-height: inherit
    }

    .uni-scroll-view-scrollbar-hidden::-webkit-scrollbar {
      display: none
    }

    .uni-scroll-view-scrollbar-hidden {
      -moz-scrollbars: none;
      scrollbar-width: none
    }

    .uni-scroll-view-content {
      width: 100%;
      height: 100%
    }

    uni-swiper-item {
      display: block;
      overflow: hidden;
      will-change: transform;
      position: absolute;
      width: 100%;
      height: 100%;
      cursor: grab
    }

    uni-swiper-item[hidden] {
      display: none
    }

    uni-swiper {
      display: block;
      height: 150px
    }

    uni-swiper[hidden] {
      display: none
    }

    .uni-swiper-wrapper {
      overflow: hidden;
      position: relative;
      width: 100%;
      height: 100%;
      transform: translateZ(0)
    }

    .uni-swiper-slides {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0
    }

    .uni-swiper-slide-frame {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      will-change: transform
    }

    .uni-swiper-dots {
      position: absolute;
      font-size: 0
    }

    .uni-swiper-dots-horizontal {
      left: 50%;
      bottom: 10px;
      text-align: center;
      white-space: nowrap;
      transform: translate(-50%)
    }

    .uni-swiper-dots-horizontal .uni-swiper-dot {
      margin-right: 8px
    }

    .uni-swiper-dots-horizontal .uni-swiper-dot:last-child {
      margin-right: 0
    }

    .uni-swiper-dots-vertical {
      right: 10px;
      top: 50%;
      text-align: right;
      transform: translateY(-50%)
    }

    .uni-swiper-dots-vertical .uni-swiper-dot {
      display: block;
      margin-bottom: 9px
    }

    .uni-swiper-dots-vertical .uni-swiper-dot:last-child {
      margin-bottom: 0
    }

    .uni-swiper-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      cursor: pointer;
      transition-property: background-color;
      transition-timing-function: ease;
      background: rgba(0, 0, 0, .3);
      border-radius: 50%
    }

    .uni-swiper-dot-active {
      background-color: #000
    }

    .uni-swiper-navigation {
      width: 26px;
      height: 26px;
      cursor: pointer;
      position: absolute;
      top: 50%;
      margin-top: -13px;
      display: flex;
      align-items: center;
      transition: all .2s;
      border-radius: 50%;
      opacity: 1
    }

    .uni-swiper-navigation-disabled {
      opacity: .35;
      cursor: not-allowed
    }

    .uni-swiper-navigation-hide {
      opacity: 0;
      cursor: auto;
      pointer-events: none
    }

    .uni-swiper-navigation-prev {
      left: 10px
    }

    .uni-swiper-navigation-prev svg {
      margin-left: -1px;
      left: 10px
    }

    .uni-swiper-navigation-prev.uni-swiper-navigation-vertical {
      top: 18px;
      left: 50%;
      margin-left: -13px
    }

    .uni-swiper-navigation-prev.uni-swiper-navigation-vertical svg {
      transform: rotate(90deg);
      margin-left: auto;
      margin-top: -2px
    }

    .uni-swiper-navigation-next {
      right: 10px
    }

    .uni-swiper-navigation-next svg {
      transform: rotate(180deg)
    }

    .uni-swiper-navigation-next.uni-swiper-navigation-vertical {
      top: auto;
      bottom: 5px;
      left: 50%;
      margin-left: -13px
    }

    .uni-swiper-navigation-next.uni-swiper-navigation-vertical svg {
      margin-top: 2px;
      transform: rotate(270deg)
    }

    uni-switch {
      -webkit-tap-highlight-color: transparent;
      display: inline-block;
      cursor: pointer
    }

    uni-switch[hidden] {
      display: none
    }

    uni-switch[disabled] {
      cursor: not-allowed
    }

    uni-switch[disabled] .uni-switch-input {
      opacity: .7
    }

    .uni-switch-wrapper {
      display: inline-flex;
      align-items: center;
      vertical-align: middle
    }

    .uni-switch-input {
      -webkit-appearance: none;
      appearance: none;
      position: relative;
      width: 52px;
      height: 32px;
      margin-right: 5px;
      border: 1px solid #dfdfdf;
      outline: 0;
      border-radius: 16px;
      box-sizing: border-box;
      background-color: #dfdfdf;
      transition: background-color .1s, border .1s
    }

    .uni-switch-input:before {
      content: " ";
      position: absolute;
      top: 0;
      left: 0;
      width: 50px;
      height: 30px;
      border-radius: 15px;
      background-color: #fdfdfd;
      transition: transform .3s
    }

    .uni-switch-input:after {
      content: " ";
      position: absolute;
      top: 0;
      left: 0;
      width: 30px;
      height: 30px;
      border-radius: 15px;
      background-color: #fff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, .4);
      transition: -webkit-transform .3s;
      transition: transform .3s;
      transition: transform .3s, -webkit-transform .3s
    }

    .uni-switch-input.uni-switch-input-checked {
      border-color: #007aff;
      background-color: #007aff
    }

    .uni-switch-input.uni-switch-input-checked:before {
      transform: scale(0)
    }

    .uni-switch-input.uni-switch-input-checked:after {
      transform: translate(20px)
    }

    uni-switch .uni-checkbox-input {
      margin-right: 5px;
      -webkit-appearance: none;
      appearance: none;
      outline: 0;
      border: 1px solid #d1d1d1;
      background-color: #fff;
      border-radius: 3px;
      width: 22px;
      height: 22px;
      position: relative;
      color: #007aff
    }

    uni-switch:not([disabled]) .uni-checkbox-input:hover {
      border-color: #007aff
    }

    uni-switch .uni-checkbox-input svg {
      fill: #007aff;
      font-size: 22px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -48%) scale(.73)
    }

    .uni-checkbox-input.uni-checkbox-input-disabled {
      background-color: #e1e1e1
    }

    .uni-checkbox-input.uni-checkbox-input-disabled:before {
      color: #adadad
    }

    uni-text[selectable] {
      cursor: auto;
      -webkit-user-select: text;
      user-select: text
    }

    uni-text {
      white-space: pre-line
    }

    uni-view {
      display: block
    }

    uni-view[hidden] {
      display: none
    }

    uni-modal {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 999;
      display: block;
      box-sizing: border-box
    }

    .uni-modal {
      position: fixed;
      z-index: 999;
      width: 80%;
      max-width: 300px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: #fff;
      text-align: center;
      border-radius: 3px;
      overflow: hidden
    }

    .uni-modal * {
      box-sizing: border-box
    }

    .uni-modal__hd {
      padding: 1em 1.6em .3em
    }

    .uni-modal__title {
      font-weight: 400;
      font-size: 18px;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical
    }

    .uni-modal__bd {
      padding: 1.3em 1.6em;
      min-height: 40px;
      font-size: 15px;
      line-height: 1.4;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      color: #999;
      max-height: 400px;
      overflow-x: hidden;
      overflow-y: auto
    }

    .uni-modal__textarea {
      resize: none;
      border: 0;
      margin: 0;
      width: 90%;
      padding: 10px;
      font-size: 20px;
      outline: none;
      border: none;
      background-color: #eee;
      text-decoration: inherit
    }

    .uni-modal__ft {
      position: relative;
      line-height: 48px;
      font-size: 18px;
      display: flex
    }

    .uni-modal__ft:after {
      content: " ";
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      height: 1px;
      border-top: 1px solid #d5d5d6;
      color: #d5d5d6;
      transform-origin: 0 0;
      transform: scaleY(.5)
    }

    .uni-modal__btn {
      display: block;
      flex: 1;
      color: #3cc51f;
      text-decoration: none;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
      position: relative;
      cursor: pointer
    }

    .uni-modal__btn:active {
      background-color: #eee
    }

    .uni-modal__btn:after {
      content: " ";
      position: absolute;
      left: 0;
      top: 0;
      width: 1px;
      bottom: 0;
      border-left: 1px solid #d5d5d6;
      color: #d5d5d6;
      transform-origin: 0 0;
      transform: scaleX(.5)
    }

    .uni-modal__btn:first-child:after {
      display: none
    }

    .uni-modal__btn_default {
      color: #353535
    }

    .uni-modal__btn_primary {
      color: #007aff
    }

    uni-toast {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 999;
      display: block;
      box-sizing: border-box;
      pointer-events: none;
      font-size: 16px
    }

    .uni-sample-toast {
      position: fixed;
      z-index: 999;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      max-width: 80%
    }

    .uni-simple-toast__text {
      display: inline-block;
      vertical-align: middle;
      color: #fff;
      background-color: rgba(17, 17, 17, .7);
      padding: 10px 20px;
      border-radius: 5px;
      font-size: 13px;
      text-align: center;
      max-width: 100%;
      word-break: break-all;
      white-space: normal
    }

    uni-toast .uni-mask {
      pointer-events: auto
    }

    .uni-toast {
      position: fixed;
      z-index: 999;
      width: 8em;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(17, 17, 17, .7);
      text-align: center;
      border-radius: 5px;
      color: #fff
    }

    .uni-toast * {
      box-sizing: border-box
    }

    .uni-toast__icon {
      margin: 20px 0 0;
      width: 38px !important;
      height: 38px !important;
      vertical-align: baseline !important
    }

    .uni-icon_toast {
      margin: 15px 0 0
    }

    .uni-icon_toast.uni-icon-success-no-circle:before {
      color: #fff;
      font-size: 55px
    }

    .uni-icon_toast.uni-loading {
      margin: 20px 0 0;
      width: 38px;
      height: 38px;
      vertical-align: baseline
    }

    .uni-toast__content {
      margin: 0 0 15px
    }
  </style>

  <meta charset="UTF-8">
  <meta name="baidu-site-verification" content="codeva-Y1vjRYeU3V">
  
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover">
  <!--head-meta-->
  <title>博义娱乐</title>
  <meta name="description" content="">
  <!--preload-links-->
  <!--app-context-->
 
 
  <script data-savepage-type="" type="text/plain"></script>
  <style data-savepage-href="/static/wangeditor.css">
    :root,
    :host {
      --w-e-textarea-bg-color: #fff;
      --w-e-textarea-color: #333;
      --w-e-textarea-border-color: #ccc;
      --w-e-textarea-slight-border-color: #e8e8e8;
      --w-e-textarea-slight-color: #d4d4d4;
      --w-e-textarea-slight-bg-color: #f5f2f0;
      --w-e-textarea-selected-border-color: #B4D5FF;
      --w-e-textarea-handler-bg-color: #4290f7;
      --w-e-toolbar-color: #595959;
      --w-e-toolbar-bg-color: #fff;
      --w-e-toolbar-active-color: #333;
      --w-e-toolbar-active-bg-color: #f1f1f1;
      --w-e-toolbar-disabled-color: #999;
      --w-e-toolbar-border-color: #e8e8e8;
      --w-e-modal-button-bg-color: #fafafa;
      --w-e-modal-button-border-color: #d9d9d9;
    }

    .w-e-text-container *,
    .w-e-toolbar * {
      box-sizing: border-box;
      margin: 0;
      outline: none;
      padding: 0
    }

    .w-e-text-container blockquote,
    .w-e-text-container li,
    .w-e-text-container p,
    .w-e-text-container td,
    .w-e-text-container th,
    .w-e-toolbar * {
      line-height: 1.5
    }

    .w-e-text-container {
      background-color: var(--w-e-textarea-bg-color);
      color: var(--w-e-textarea-color);
      height: 100%;
      position: relative
    }

    .w-e-text-container .w-e-scroll {
      -webkit-overflow-scrolling: touch;
      height: 100%
    }

    .w-e-text-container [data-slate-editor] {
      word-wrap: break-word;
      border-top: 1px solid transparent;
      min-height: 100%;
      outline: 0;
      padding: 0 10px;
      white-space: pre-wrap
    }

    .w-e-text-container [data-slate-editor] p {
      margin: 15px 0
    }

    .w-e-text-container [data-slate-editor] h1,
    .w-e-text-container [data-slate-editor] h2,
    .w-e-text-container [data-slate-editor] h3,
    .w-e-text-container [data-slate-editor] h4,
    .w-e-text-container [data-slate-editor] h5 {
      margin: 20px 0
    }

    .w-e-text-container [data-slate-editor] img {
      cursor: default;
      display: inline !important;
      max-width: 100%;
      min-height: 20px;
      min-width: 20px
    }

    .w-e-text-container [data-slate-editor] span {
      text-indent: 0
    }

    .w-e-text-container [data-slate-editor] [data-selected=true] {
      box-shadow: 0 0 0 2px var(--w-e-textarea-selected-border-color)
    }

    .w-e-text-placeholder {
      font-style: italic;
      left: 10px;
      top: 17px;
      width: 90%
    }

    .w-e-max-length-info,
    .w-e-text-placeholder {
      color: var(--w-e-textarea-slight-color);
      pointer-events: none;
      position: absolute;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none
    }

    .w-e-max-length-info {
      bottom: .5em;
      right: 1em
    }

    .w-e-bar {
      background-color: var(--w-e-toolbar-bg-color);
      color: var(--w-e-toolbar-color);
      font-size: 14px;
      padding: 0 5px
    }

    .w-e-bar svg {
      fill: var(--w-e-toolbar-color);
      height: 14px;
      width: 14px
    }

    .w-e-bar-show {
      display: flex
    }

    .w-e-bar-hidden {
      display: none
    }

    .w-e-hover-bar {
      border: 1px solid var(--w-e-toolbar-border-color);
      border-radius: 3px;
      box-shadow: 0 2px 5px #0000001f;
      position: absolute
    }

    .w-e-toolbar {
      flex-wrap: wrap;
      position: relative
    }

    .w-e-bar-divider {
      background-color: var(--w-e-toolbar-border-color);
      display: inline-flex;
      height: 40px;
      margin: 0 5px;
      width: 1px
    }

    .w-e-bar-item {
      display: flex;
      height: 40px;
      padding: 4px;
      position: relative;
      text-align: center
    }

    .w-e-bar-item,
    .w-e-bar-item button {
      align-items: center;
      justify-content: center
    }

    .w-e-bar-item button {
      background: transparent;
      border: none;
      color: var(--w-e-toolbar-color);
      cursor: pointer;
      display: inline-flex;
      height: 32px;
      overflow: hidden;
      padding: 0 8px;
      white-space: nowrap
    }

    .w-e-bar-item button:hover {
      background-color: var(--w-e-toolbar-active-bg-color);
      color: var(--w-e-toolbar-active-color)
    }

    .w-e-bar-item button .title {
      margin-left: 5px
    }

    .w-e-bar-item .active {
      background-color: var(--w-e-toolbar-active-bg-color);
      color: var(--w-e-toolbar-active-color)
    }

    .w-e-bar-item .disabled {
      color: var(--w-e-toolbar-disabled-color);
      cursor: not-allowed
    }

    .w-e-bar-item .disabled svg {
      fill: var(--w-e-toolbar-disabled-color)
    }

    .w-e-bar-item .disabled:hover {
      background-color: var(--w-e-toolbar-bg-color);
      color: var(--w-e-toolbar-disabled-color)
    }

    .w-e-bar-item .disabled:hover svg {
      fill: var(--w-e-toolbar-disabled-color)
    }

    .w-e-menu-tooltip-v5:before {
      background-color: var(--w-e-toolbar-active-color);
      border-radius: 5px;
      color: var(--w-e-toolbar-bg-color);
      content: attr(data-tooltip);
      font-size: .75em;
      opacity: 0;
      padding: 5px 10px;
      position: absolute;
      text-align: center;
      top: 40px;
      transition: opacity .6s;
      visibility: hidden;
      white-space: pre;
      z-index: 1
    }

    .w-e-menu-tooltip-v5:after {
      border: 5px solid transparent;
      border-bottom: 5px solid var(--w-e-toolbar-active-color);
      content: "";
      opacity: 0;
      position: absolute;
      top: 30px;
      transition: opacity .6s;
      visibility: hidden
    }

    .w-e-menu-tooltip-v5:hover:after,
    .w-e-menu-tooltip-v5:hover:before {
      opacity: 1;
      visibility: visible
    }

    .w-e-menu-tooltip-v5.tooltip-right:before {
      left: 100%;
      top: 10px
    }

    .w-e-menu-tooltip-v5.tooltip-right:after {
      border-bottom-color: transparent;
      border-left-color: transparent;
      border-right-color: var(--w-e-toolbar-active-color);
      border-top-color: transparent;
      left: 100%;
      margin-left: -10px;
      top: 16px
    }

    .w-e-bar-item-group .w-e-bar-item-menus-container {
      background-color: var(--w-e-toolbar-bg-color);
      border: 1px solid var(--w-e-toolbar-border-color);
      border-radius: 3px;
      box-shadow: 0 2px 10px #0000001f;
      display: none;
      left: 0;
      margin-top: 40px;
      position: absolute;
      top: 0;
      z-index: 1
    }

    .w-e-bar-item-group:hover .w-e-bar-item-menus-container {
      display: block
    }

    .w-e-select-list {
      background-color: var(--w-e-toolbar-bg-color);
      border: 1px solid var(--w-e-toolbar-border-color);
      border-radius: 3px;
      box-shadow: 0 2px 10px #0000001f;
      left: 0;
      margin-top: 40px;
      max-height: 350px;
      min-width: 100px;
      overflow-y: auto;
      position: absolute;
      top: 0;
      z-index: 1
    }

    .w-e-select-list ul {
      line-height: 1;
      list-style: none
    }

    .w-e-select-list ul .selected {
      background-color: var(--w-e-toolbar-active-bg-color)
    }

    .w-e-select-list ul li {
      cursor: pointer;
      padding: 7px 0 7px 25px;
      position: relative;
      text-align: left;
      white-space: nowrap
    }

    .w-e-select-list ul li:hover {
      background-color: var(--w-e-toolbar-active-bg-color)
    }

    .w-e-select-list ul li svg {
      left: 0;
      margin-left: 5px;
      margin-top: -7px;
      position: absolute;
      top: 50%
    }

    .w-e-bar-bottom .w-e-select-list {
      bottom: 0;
      margin-bottom: 40px;
      margin-top: 0;
      top: inherit
    }

    .w-e-drop-panel {
      background-color: var(--w-e-toolbar-bg-color);
      border: 1px solid var(--w-e-toolbar-border-color);
      border-radius: 3px;
      box-shadow: 0 2px 10px #0000001f;
      margin-top: 40px;
      min-width: 200px;
      padding: 10px;
      position: absolute;
      top: 0;
      z-index: 1
    }

    .w-e-bar-bottom .w-e-drop-panel {
      bottom: 0;
      margin-bottom: 40px;
      margin-top: 0;
      top: inherit
    }

    .w-e-modal {
      background-color: var(--w-e-toolbar-bg-color);
      border: 1px solid var(--w-e-toolbar-border-color);
      border-radius: 3px;
      box-shadow: 0 2px 10px #0000001f;
      color: var(--w-e-toolbar-color);
      font-size: 14px;
      min-height: 40px;
      min-width: 100px;
      padding: 20px 15px 0;
      position: absolute;
      text-align: left;
      z-index: 1
    }

    .w-e-modal .btn-close {
      cursor: pointer;
      line-height: 1;
      padding: 5px;
      position: absolute;
      right: 8px;
      top: 7px
    }

    .w-e-modal .btn-close svg {
      fill: var(--w-e-toolbar-color);
      height: 10px;
      width: 10px
    }

    .w-e-modal .babel-container {
      display: block;
      margin-bottom: 15px
    }

    .w-e-modal .babel-container span {
      display: block;
      margin-bottom: 10px
    }

    .w-e-modal .button-container {
      margin-bottom: 15px
    }

    .w-e-modal button {
      background-color: var(--w-e-modal-button-bg-color);
      border: 1px solid var(--w-e-modal-button-border-color);
      border-radius: 4px;
      color: var(--w-e-toolbar-color);
      cursor: pointer;
      font-weight: 400;
      height: 32px;
      padding: 4.5px 15px;
      text-align: center;
      touch-action: manipulation;
      transition: all .3s cubic-bezier(.645, .045, .355, 1);
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none;
      white-space: nowrap
    }

    .w-e-modal input[type=number],
    .w-e-modal input[type=text],
    .w-e-modal textarea {
      font-feature-settings: "tnum";
      background-color: var(--w-e-toolbar-bg-color);
      border: 1px solid var(--w-e-modal-button-border-color);
      border-radius: 4px;
      color: var(--w-e-toolbar-color);
      font-variant: tabular-nums;
      padding: 4.5px 11px;
      transition: all .3s;
      width: 100%
    }

    .w-e-modal textarea {
      min-height: 60px
    }

    body .w-e-modal,
    body .w-e-modal * {
      box-sizing: border-box
    }

    .w-e-progress-bar {
      background-color: var(--w-e-textarea-handler-bg-color);
      height: 1px;
      position: absolute;
      transition: width .3s;
      width: 0
    }

    .w-e-full-screen-container {
      bottom: 0 !important;
      display: flex !important;
      flex-direction: column !important;
      height: 100% !important;
      left: 0 !important;
      margin: 0 !important;
      padding: 0 !important;
      position: fixed;
      right: 0 !important;
      top: 0 !important;
      width: 100% !important
    }

    .w-e-full-screen-container [data-w-e-textarea=true] {
      flex: 1 !important
    }

    .w-e-text-container [data-slate-editor] code {
      background-color: var(--w-e-textarea-slight-bg-color);
      border-radius: 3px;
      font-family: monospace;
      padding: 3px
    }

    .w-e-panel-content-color {
      list-style: none;
      text-align: left;
      width: 230px
    }

    .w-e-panel-content-color li {
      border: 1px solid var(--w-e-toolbar-bg-color);
      border-radius: 3px 3px;
      cursor: pointer;
      display: inline-block;
      padding: 2px
    }

    .w-e-panel-content-color li:hover {
      border-color: var(--w-e-toolbar-color)
    }

    .w-e-panel-content-color li .color-block {
      border: 1px solid var(--w-e-toolbar-border-color);
      border-radius: 3px 3px;
      height: 17px;
      width: 17px
    }

    .w-e-panel-content-color .active {
      border-color: var(--w-e-toolbar-color)
    }

    .w-e-panel-content-color .clear {
      line-height: 1.5;
      margin-bottom: 5px;
      width: 100%
    }

    .w-e-panel-content-color .clear svg {
      height: 16px;
      margin-bottom: -4px;
      width: 16px
    }

    .w-e-text-container [data-slate-editor] blockquote {
      background-color: var(--w-e-textarea-slight-bg-color);
      border-left: 8px solid var(--w-e-textarea-selected-border-color);
      display: block;
      font-size: 100%;
      line-height: 1.5;
      margin: 10px 0;
      padding: 10px
    }

    .w-e-panel-content-emotion {
      font-size: 20px;
      list-style: none;
      text-align: left;
      width: 300px
    }

    .w-e-panel-content-emotion li {
      border-radius: 3px 3px;
      cursor: pointer;
      display: inline-block;
      padding: 0 5px
    }

    .w-e-panel-content-emotion li:hover {
      background-color: var(--w-e-textarea-slight-bg-color)
    }

    .w-e-textarea-divider {
      border-radius: 3px;
      margin: 20px auto;
      padding: 20px
    }

    .w-e-textarea-divider hr {
      background-color: var(--w-e-textarea-border-color);
      border: 0;
      display: block;
      height: 1px
    }

    .w-e-text-container [data-slate-editor] pre>code {
      background-color: var(--w-e-textarea-slight-bg-color);
      border: 1px solid var(--w-e-textarea-slight-border-color);
      border-radius: 4px 4px;
      display: block;
      font-size: 14px;
      padding: 10px;
      text-indent: 0
    }

    .w-e-text-container [data-slate-editor] .w-e-image-container {
      display: inline-block;
      margin: 0 3px
    }

    .w-e-text-container [data-slate-editor] .w-e-image-container:hover {
      box-shadow: 0 0 0 2px var(--w-e-textarea-selected-border-color)
    }

    .w-e-text-container [data-slate-editor] .w-e-selected-image-container {
      overflow: hidden;
      position: relative
    }

    .w-e-text-container [data-slate-editor] .w-e-selected-image-container .w-e-image-dragger {
      background-color: var(--w-e-textarea-handler-bg-color);
      height: 7px;
      position: absolute;
      width: 7px
    }

    .w-e-text-container [data-slate-editor] .w-e-selected-image-container .left-top {
      cursor: nwse-resize;
      left: 0;
      top: 0
    }

    .w-e-text-container [data-slate-editor] .w-e-selected-image-container .right-top {
      cursor: nesw-resize;
      right: 0;
      top: 0
    }

    .w-e-text-container [data-slate-editor] .w-e-selected-image-container .left-bottom {
      bottom: 0;
      cursor: nesw-resize;
      left: 0
    }

    .w-e-text-container [data-slate-editor] .w-e-selected-image-container .right-bottom {
      bottom: 0;
      cursor: nwse-resize;
      right: 0
    }

    .w-e-text-container [data-slate-editor] .w-e-selected-image-container:hover {
      box-shadow: none
    }

    .w-e-text-container [contenteditable=false] .w-e-image-container:hover {
      box-shadow: none
    }

    .w-e-text-container [data-slate-editor] .table-container {
      border: 1px dashed var(--w-e-textarea-border-color);
      border-radius: 5px;
      margin-top: 10px;
      overflow-x: auto;
      padding: 10px;
      width: 100%
    }

    .w-e-text-container [data-slate-editor] table {
      border-collapse: collapse
    }

    .w-e-text-container [data-slate-editor] table td,
    .w-e-text-container [data-slate-editor] table th {
      border: 1px solid var(--w-e-textarea-border-color);
      line-height: 1.5;
      min-width: 30px;
      padding: 3px 5px;
      text-align: left
    }

    .w-e-text-container [data-slate-editor] table th {
      background-color: var(--w-e-textarea-slight-bg-color);
      font-weight: 700;
      text-align: center
    }

    .w-e-panel-content-table {
      background-color: var(--w-e-toolbar-bg-color)
    }

    .w-e-panel-content-table table {
      border-collapse: collapse
    }

    .w-e-panel-content-table td {
      border: 1px solid var(--w-e-toolbar-border-color);
      cursor: pointer;
      height: 15px;
      padding: 3px 5px;
      width: 20px
    }

    .w-e-panel-content-table td.active {
      background-color: var(--w-e-toolbar-active-bg-color)
    }

    .w-e-textarea-video-container {
      background-image: linear-gradient(45deg, #eee 25%, transparent 0, transparent 75%, #eee 0, #eee), linear-gradient(45deg, #eee 25%, #fff 0, #fff 75%, #eee 0, #eee);
      background-position: 0 0, 10px 10px;
      background-size: 20px 20px;
      border: 1px dashed var(--w-e-textarea-border-color);
      border-radius: 5px;
      margin: 10px auto 0;
      padding: 10px 0;
      text-align: center
    }

    .w-e-text-container [data-slate-editor] pre>code {
      word-wrap: normal;
      font-family: Consolas, Monaco, Andale Mono, Ubuntu Mono, monospace;
      -webkit-hyphens: none;
      hyphens: none;
      line-height: 1.5;
      margin: .5em 0;
      overflow: auto;
      padding: 1em;
      -moz-tab-size: 4;
      -o-tab-size: 4;
      tab-size: 4;
      text-align: left;
      text-shadow: 0 1px #fff;
      white-space: pre;
      word-break: normal;
      word-spacing: normal
    }

    .w-e-text-container [data-slate-editor] pre>code .token.cdata,
    .w-e-text-container [data-slate-editor] pre>code .token.comment,
    .w-e-text-container [data-slate-editor] pre>code .token.doctype,
    .w-e-text-container [data-slate-editor] pre>code .token.prolog {
      color: #708090
    }

    .w-e-text-container [data-slate-editor] pre>code .token.punctuation {
      color: #999
    }

    .w-e-text-container [data-slate-editor] pre>code .token.namespace {
      opacity: .7
    }

    .w-e-text-container [data-slate-editor] pre>code .token.boolean,
    .w-e-text-container [data-slate-editor] pre>code .token.constant,
    .w-e-text-container [data-slate-editor] pre>code .token.deleted,
    .w-e-text-container [data-slate-editor] pre>code .token.number,
    .w-e-text-container [data-slate-editor] pre>code .token.property,
    .w-e-text-container [data-slate-editor] pre>code .token.symbol,
    .w-e-text-container [data-slate-editor] pre>code .token.tag {
      color: #905
    }

    .w-e-text-container [data-slate-editor] pre>code .token.attr-name,
    .w-e-text-container [data-slate-editor] pre>code .token.builtin,
    .w-e-text-container [data-slate-editor] pre>code .token.char,
    .w-e-text-container [data-slate-editor] pre>code .token.inserted,
    .w-e-text-container [data-slate-editor] pre>code .token.selector,
    .w-e-text-container [data-slate-editor] pre>code .token.string {
      color: #690
    }

    .w-e-text-container [data-slate-editor] pre>code .language-css .token.string,
    .w-e-text-container [data-slate-editor] pre>code .style .token.string,
    .w-e-text-container [data-slate-editor] pre>code .token.entity,
    .w-e-text-container [data-slate-editor] pre>code .token.operator,
    .w-e-text-container [data-slate-editor] pre>code .token.url {
      color: #9a6e3a
    }

    .w-e-text-container [data-slate-editor] pre>code .token.atrule,
    .w-e-text-container [data-slate-editor] pre>code .token.attr-value,
    .w-e-text-container [data-slate-editor] pre>code .token.keyword {
      color: #07a
    }

    .w-e-text-container [data-slate-editor] pre>code .token.class-name,
    .w-e-text-container [data-slate-editor] pre>code .token.function {
      color: #dd4a68
    }

    .w-e-text-container [data-slate-editor] pre>code .token.important,
    .w-e-text-container [data-slate-editor] pre>code .token.regex,
    .w-e-text-container [data-slate-editor] pre>code .token.variable {
      color: #e90
    }

    .w-e-text-container [data-slate-editor] pre>code .token.bold,
    .w-e-text-container [data-slate-editor] pre>code .token.important {
      font-weight: 700
    }

    .w-e-text-container [data-slate-editor] pre>code .token.italic {
      font-style: italic
    }

    .w-e-text-container [data-slate-editor] pre>code .token.entity {
      cursor: help
    }
  </style>

  <script data-savepage-type="module" type="text/plain" crossorigin=""
    data-savepage-src="/assets/index-Bot1eTtR.js"></script>
  <style data-savepage-href="/assets/index-D28xNmUL.css">
    .uniui-color[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-wallet[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-settings-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-auth-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-shop-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-staff-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-vip-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-plus-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-folder-add-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-color-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-tune-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-calendar-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-notification-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-wallet-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-medal-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-gift-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-fire-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-refreshempty[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-location-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-person-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-personadd-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-back[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-forward[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrow-right[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrowthinright[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrow-left[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrowthinleft[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrow-up[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrowthinup[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrow-down[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrowthindown[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-bottom[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrowdown[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-right[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrowright[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-top[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrowup[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-left[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-arrowleft[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-eye[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-eye-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-eye-slash[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-eye-slash-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-info-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-reload[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-micoff-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-map-pin-ellipse[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-map-pin[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-location[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-starhalf[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-star[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-star-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-calendar[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-fire[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-medal[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-font[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-gift[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-link[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-notification[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-staff[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-vip[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-folder-add[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-tune[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-auth[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-person[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-email-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-phone-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-phone[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-email[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-personadd[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-chatboxes-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-contact[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-chatbubble-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-contact-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-chatboxes[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-chatbubble[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-upload-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-upload[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-weixin[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-compose[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-qq[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-download-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-pyq[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-sound[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-trash-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-sound-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-trash[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-videocam-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-spinner-cycle[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-weibo[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-videocam[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-download[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-help[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-navigate-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-plusempty[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-smallcircle[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-minus-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-micoff[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-closeempty[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-clear[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-navigate[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-minus[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-image[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-mic[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-paperplane[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-close[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-help-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-paperplane-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-plus[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-mic-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-image-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-locked-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-info[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-locked[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-camera-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-chat-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-camera[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-circle[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-checkmarkempty[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-chat[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-circle-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-flag[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-flag-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-gear-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-home[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-home-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-gear[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-smallcircle-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-map-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-map[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-refresh-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-refresh[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-cloud-upload[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-cloud-download-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-cloud-download[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-cloud-upload-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-redo[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-images-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-undo-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-more[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-more-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-undo[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-images[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-paperclip[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-settings[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-search[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-redo-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-list[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-mail-open-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-hand-down-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-hand-down[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-hand-up-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-hand-up[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-heart-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-mail-open[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-heart[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-loop[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-pulldown[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-scan[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-bars[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-cart-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-checkbox[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-checkbox-filled[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-shop[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-headphones[data-v-7c2f6cb0]:before {
      content: ""
    }

    .uniui-cart[data-v-7c2f6cb0]:before {
      content: ""
    }

    @font-face {
      font-family: uniicons;
      src:
        /*savepage-url=/assets/uniicons-ClHF27D8.ttf*/
    }

    .uni-icons[data-v-7c2f6cb0] {
      font-family: uniicons;
      text-decoration: none;
      text-align: center
    }

    .avatar[data-v-415ba65a] {
      border-radius: 50%;
      box-shadow: 0 0 0 1px rgba(240, 246, 252, .1) inset;
      flex-shrink: 0;
      position: relative
    }

    .avatar .avatar-image[data-v-415ba65a] {
      border-radius: 50%;
      overflow: hidden
    }

    .avatar .verified-icon[data-v-415ba65a] {
      position: absolute;
      right: -3px;
      bottom: 0;
      background-color: #000;
      border: 1px solid var(--color-primary);
      color: var(--color-primary);
      border-radius: 50%;
      padding: 0
    }

    .dropdown[data-v-5f3a6bb2] {
      position: relative;
      color: var(--color-text-normal);
      cursor: pointer;
      padding: 0 .75rem;
      height: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      line-height: 1
    }

    .dropdown.fullscreen[data-v-5f3a6bb2] {
      position: static
    }

    .dropdown.active .menu-list-wrapper[data-v-5f3a6bb2] {
      display: block
    }

    .dropdown[data-v-5f3a6bb2] .uni-icons {
      margin-left: .25rem
    }

    .dropdown .menu-image[data-v-5f3a6bb2] {
      width: 1.5rem;
      height: 1.5rem;
      margin-right: .375rem
    }

    .dropdown .menu-list-wrapper[data-v-5f3a6bb2] {
      z-index: 1;
      min-width: 100%;
      display: none;
      position: absolute;
      left: 0;
      top: 100%
    }

    .dropdown .menu-list-wrapper.right[data-v-5f3a6bb2] {
      left: auto;
      right: 0
    }

    .dropdown .menu-list[data-v-5f3a6bb2] {
      padding: 6px 0;
      box-shadow: 0 1px 5px rgba(0, 0, 0, .75);
      background-color: var(--color-bg);
      margin-top: .375rem;
      width: 120px;
      min-width: 100%
    }

    .dropdown .menu-list .menu-list-item[data-v-5f3a6bb2] {
      display: flex;
      flex-direction: row;
      align-items: center
    }

    .dropdown .menu-list .menu-list-item:hover .menu-item[data-v-5f3a6bb2] {
      color: var(--color-primary)
    }

    .dropdown .menu-list[data-v-5f3a6bb2] .navigator-wrap {
      flex: 1;
      display: block
    }

    .dropdown .menu-list .menu-item[data-v-5f3a6bb2] {
      padding: 8px 12px;
      text-align: left;
      color: var(--color-text-normal)
    }

    .dropdown .menu-list .menu-image+.menu-item[data-v-5f3a6bb2] {
      padding-left: 0
    }

    .dropdown .menu-fullscreen[data-v-5f3a6bb2] {
      display: flex;
      flex-direction: row;
      padding: .75rem;
      text-align: left
    }

    .dropdown .menu-fullscreen .column[data-v-5f3a6bb2] {
      flex: 1
    }

    .dropdown .menu-fullscreen .column+.column[data-v-5f3a6bb2] {
      margin-left: .75rem
    }

    .dropdown .menu-fullscreen .group+.group[data-v-5f3a6bb2] {
      margin-top: .75rem
    }

    .dropdown .menu-fullscreen .group-name[data-v-5f3a6bb2] {
      background: var(--color-bg-lightest);
      padding: .375rem;
      margin-bottom: .5rem;
      display: flex;
      align-items: center;
      flex-direction: row
    }

    .dropdown .menu-fullscreen .link-list[data-v-5f3a6bb2] {
      display: flex;
      flex-direction: row
    }

    .dropdown .menu-fullscreen .link-list .group-link[data-v-5f3a6bb2] {
      margin-left: .375rem
    }

    .dropdown .menu-fullscreen .group-link[data-v-5f3a6bb2] {
      margin-top: .375rem;
      font-size: .875rem;
      line-height: 20px
    }

    .dropdown .menu-fullscreen .group-link-nav[data-v-5f3a6bb2]:hover {
      color: var(--color-primary)
    }

    .uni-badge--x[data-v-1ca6063a] {
      display: inline-block;
      position: relative
    }

    .uni-badge--absolute[data-v-1ca6063a] {
      position: absolute
    }

    .uni-badge--small[data-v-1ca6063a] {
      transform: scale(.8);
      transform-origin: center center
    }

    .uni-badge[data-v-1ca6063a] {
      display: flex;
      overflow: hidden;
      box-sizing: border-box;
      justify-content: center;
      flex-direction: row;
      height: 20px;
      min-width: 20px;
      padding: 0 4px;
      line-height: 18px;
      color: #fff;
      border-radius: 100px;
      background-color: #8f939c;
      background-color: transparent;
      border: 1px solid #fff;
      text-align: center;
      font-family: Helvetica Neue, Helvetica, sans-serif;
      font-feature-settings: "tnum";
      font-size: 12px;
      z-index: 999;
      cursor: pointer
    }

   

    @media (min-width: 1024px) {
      .top-bar[data-v-884e8d98] {
        display: none
      }

      .top-bar.global[data-v-884e8d98] {
        display: block
      }

      .top-bar-content[data-v-884e8d98] {
        height: 44px
      }

      .logo[data-v-884e8d98] {
        width: 84px;
        margin-left: 0
      }

      .top-bar-content .left .game-nav[data-v-884e8d98] {
        display: block
      }

      .nav .game-nav[data-v-884e8d98] {
        display: none
      }
    }

    .change-lang-dropdown[data-v-c0a53fd3] {
      padding: 0;
      margin-left: .375rem
    }

    .change-ptr[data-v-c0a53fd3] {
      color: var(--d4-color-label);
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: .375rem .5rem
    }

    .change-ptr .lang-text[data-v-c0a53fd3] {
      margin-left: .375rem
    }

    .change-ptr[data-v-c0a53fd3] .menu-item {
      text-align: right
    }

    .top-window[data-v-c3e5a1de] {
      background-color: var(--color-bg);
      height: 80px;
      border-bottom: 1px solid #111111;
      box-shadow: 0 1px 5px rgba(0, 0, 0, .75);
      display: block;
      line-height: 1
    }

  
    .line-affix.hide-affix[data-v-09bb0cb9]:before {
      display: none
    }

    @media (min-width: 1024px) {
      .c_group[data-v-09bb0cb9] {
        width: 20%
      }
    }

    [data-v-f25b737a]:root {
      color-scheme: dark;
      --color-unique: #bd9b4e;
      --color-magic: #84b7ff;
      --color-rare: #ffff00;
      --color-set: #00ff00;
      --color-runeword: #e9c964;
      --color-rune: #ee7a03;
      --color-craft: #fab100;
      --color-socket: #888;
      --color-normal: #d8d8d8;
      --color-set-bonus: #cebc86;
      --color-red: #ff5353;
      --color-terror: #bf23bf;
      --d4-color-label: #ad9d8e;
      --d4-color-resource: #e3e79d;
      --d4-color-number: #ffc772;
      --d4-color-important: #e3e0d9;
      --d4-color-green: #43d630;
      --d4-color-heart: #c3fffa;
      --d4-color-summon: #5fc3f1;
      --d4-color-mythic: #cda1d8;
      --d4-color-unique: #f7c99c;
      --d4-color-legendary: #f8930a;
      --d4-color-rare: #f1f25c;
      --d4-color-magic: #6d9eeb;
      --d4-color-normal: #ffffff;
      --poe2-color-gem: #1ba29b;
      --poe2-color-tooltip: #7f7f7f;
      --color-pink: #af166a;
      --color-primary: #e9c964;
      --color-bg: #1b1b1b;
      --color-bg-light: #212121;
      --color-bg-lightest: #333333;
      --color-bg-dark: #121212;
      --color-tooltip: #bab0a3;
      --color-text-normal: #c0c0c0;
      --color-text-grey: #8a8a8a;
      --color-text-title: #e3e3e3;
      --font-size-xl: 1.125rem;
      --font-size-large: 1rem;
      --font-size-normal: .875rem;
      --font-size-small: .75rem;
      --font-size-xs: .625rem;
      --color-border: #3b3b3b;
      --color-control-border: #242424;
      --safe-area-inset-top: 0;
      --safe-area-inset-top: constant(safe-area-inset-top);
      --safe-area-inset-top: env(safe-area-inset-top);
      --safe-area-inset-bottom: 0;
      --safe-area-inset-bottom: constant(safe-area-inset-bottom);
      --safe-area-inset-bottom: env(safe-area-inset-bottom);
      --padding-hoz: .75rem
    }

   

   

    .database-item.d4.tooltip .item-title[data-v-f25b737a] {
      font-size: 1.5rem;
      font-family: Georgia, Times New Roman, Songti, SimSun, Heiti, serif;
      font-weight: 700
    }

    .database-item.d4.tooltip .item-seperator[data-v-f25b737a] {
      background-position-x: center
    }

    .database-item.d4.tooltip .item-banner[data-v-f25b737a] {
      width: auto
    }

    .uni-list-item:first-child .database-item.list[data-v-f25b737a] {
      padding-top: 0
    }

    .uni-list-item:last-child .database-item-line[data-v-f25b737a]:last-child {
      display: none
    }

    .uni-scroll-view-content>.database-item[data-v-f25b737a]:first-child {
      padding-top: 0
    }


    
    @media (min-width: 1024px) {
      .dropdown-menu.border[data-v-d292d276] .van-dropdown-item--down {
        transform: translateY(2px)
      }

      .dropdown-menu[data-v-d292d276] .van-dropdown-menu__title {
        width: 100%
      }

      .dropdown-menu[data-v-d292d276] .van-dropdown-item--down {
        position: absolute;
        top: 100% !important;
        left: 0;
        right: 0;
        height: 200px
      }
    }

    @keyframes spin-f0d315a3 {
      0% {
        transform: rotate(0)
      }

      to {
        transform: rotate(360deg)
      }
    }

    .button[data-v-f0d315a3] {
      cursor: pointer;
      background-color: #ffd54d;
      height: 1.875rem;
      display: flex;
      flex: 1;
      justify-content: center;
      align-items: center;
      padding: 0 1rem;
      border-radius: .25rem;
      text-shadow: none
    }

    .button.round[data-v-f0d315a3] {
      border-radius: .9375rem
    }

    .button[data-v-f0d315a3]:hover {
      background-color: #ffdb66
    }

    .button.disabled[data-v-f0d315a3] {
      opacity: .4
    }

    .button.normal[data-v-f0d315a3] {
      background-color: transparent;
      border: .0625rem solid #e9c964
    }

    .button.normal .button-text[data-v-f0d315a3] {
      color: #e9c964
    }

    .button.normal[data-v-f0d315a3]:hover {
      background-color: transparent
    }

    .button.small[data-v-f0d315a3] {
      height: 1.25rem;
      padding: 0 .5rem;
      border-radius: .125rem
    }

    .button.small .button-text[data-v-f0d315a3] {
      font-size: .75rem;
      font-size: var(--font-size-small)
    }

    .button.large[data-v-f0d315a3] {
      height: auto;
      padding: .5rem 1.5rem
    }

    .button.loading .uni-icons[data-v-f0d315a3] {
      animation: spin-f0d315a3 1s linear infinite
    }

    .button-text[data-v-f0d315a3] {
      white-space: nowrap;
      color: #000;
      text-align: center;
      font-size: .875rem;
      font-size: var(--font-size-normal)
    }

    .login-page[data-v-26fcf888] {
      padding: 1.25rem;
      --van-field-label-width: 3.125rem;
      text-align: center;
      position: relative
    }

    .login-page .close-icon[data-v-26fcf888] {
      position: absolute;
      top: 1rem;
      right: 1rem;
      cursor: pointer;
      color: var(--color-text-grey)
    }

    .login-page .close-icon[data-v-26fcf888]:hover {
      color: var(--color-text-normal)
    }

    .login-page[data-v-26fcf888] .van-cell-group--inset {
      overflow: visible
    }

    .login-page[data-v-26fcf888] .van-cell-group .van-cell {
      overflow: visible;
      background: transparent
    }

    .login-page[data-v-26fcf888] .van-field__right-icon {
      cursor: pointer
    }

    .login-page .regForm[data-v-26fcf888] {
      --van-field-label-width: 4.0625rem
    }

    .login-page .title[data-v-26fcf888] {
      font-size: 1.5rem;
      margin-bottom: 2.5rem;
      color: var(--color-primary)
    }

    .login-page .subTitle[data-v-26fcf888] {
      font-size: .875rem;
      color: var(--color-text-grey);
      margin-top: .375rem;
      margin-bottom: 2.5rem
    }

    .login-page .dropdown-wrap[data-v-26fcf888] {
      position: static;
      padding-right: 0;
      --van-dropdown-menu-title-text-color: var(--d4-color-label);
      --van-overlay-background: transparent
    }

    .login-page .dropdown-wrap[data-v-26fcf888] .van-dropdown-menu__title:after {
      border-color: transparent transparent currentColor currentColor
    }

    .login-page .dropdown-wrap[data-v-26fcf888] .van-dropdown-item--down {
      position: absolute;
      top: 100% !important;
      left: 0;
      right: 0;
      height: 200px
    }

    .login-page .dropdown-wrap[data-v-26fcf888] .van-dropdown-menu__title {
      padding: 0;
      width: 100%
    }

    .login-page .dropdown-wrap[data-v-26fcf888] .van-dropdown-menu__title:after {
      right: 0
    }

    .login-page .send-verify[data-v-26fcf888] {
      color: var(--color-primary);
      cursor: pointer
    }

    .login-page .send-verify.counting[data-v-26fcf888] {
      color: var(--color-text-grey)
    }

    .login-page .btn[data-v-26fcf888] {
      margin-top: 2rem
    }

    .login-page .bottom[data-v-26fcf888] {
      margin-top: 1.5rem
    }

    .login-page .tip[data-v-26fcf888] {
      font-size: .875rem;
      color: var(--color-text-grey);
      margin-top: .375rem
    }

    .login-page .link[data-v-26fcf888] {
      margin-left: .25rem;
      color: var(--d4-color-unique);
      cursor: pointer
    }

    .login-page .link[data-v-26fcf888]:hover {
      color: var(--color-primary)
    }

    .login-page .login-option-list[data-v-26fcf888] {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: row;
      margin-bottom: 1.25rem
    }

    .login-page .login-option-list .login-option[data-v-26fcf888] {
      color: var(--color-text-grey);
      cursor: pointer
    }

    .login-page .login-option-list .login-option.active[data-v-26fcf888],
    .login-page .login-option-list .login-option[data-v-26fcf888]:hover {
      color: var(--color-text-normal)
    }

    .login-page .login-option-list .login-option+.login-option[data-v-26fcf888] {
      margin-left: .75rem
    }

    .login-page .error[data-v-26fcf888] {
      font-size: .875rem;
      color: var(--color-red);
      margin-top: .75rem
    }

    .login-page .login-header[data-v-26fcf888] {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      margin-bottom: .75rem
    }

    .login-page .login-header .header-left[data-v-26fcf888] {
      font-size: 1.25rem;
      color: var(--d4-color-label)
    }

    .login-page .login-header .tip[data-v-26fcf888] {
      margin: 0
    }

    .login-page .login-footer[data-v-26fcf888] {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: .75rem;
      font-size: .875rem
    }

    .login-page .login-footer .link[data-v-26fcf888] {
      color: var(--color-text-grey)
    }

    .login-page .login-footer .link[data-v-26fcf888]:hover {
      color: var(--color-primary)
    }

    @media (min-width: 1024px) {
      .login-page[data-v-26fcf888] {
        padding: 2.5rem 2.5rem 1.875rem
      }

      .btn[data-v-26fcf888] {
        margin: 0 auto
      }
    }

    .right-window[data-v-1af98467] {
      width: 1024px;
      margin: 0 auto;
      position: relative
    }

    .right-window .right-content[data-v-1af98467] {
      position: absolute;
      right: -220px;
      top: 92px;
      width: 200px
    }

    .right-window .tip[data-v-1af98467] {
      background-color: #111;
      padding: 12px 20px;
      margin-top: 12px;
      line-height: 1.5
    }

    .right-window .qrcode[data-v-1af98467] {
      background-color: #111;
      padding: 20px;
      border-radius: 4px;
      text-align: center
    }

    .right-window .qrcode-image[data-v-1af98467] {
      width: 160px;
      height: 160px
    }

    .right-window .title[data-v-1af98467] {
      font-size: 18px;
      color: var(--color-primary);
      margin-top: 8px
    }

    .right-window .desc[data-v-1af98467] {
      font-size: 16px;
      margin-top: 8px
    }

    .right-window .business-wrapper[data-v-1af98467] {
      display: flex;
      margin-top: .75rem
    }

    .right-window .business-image[data-v-1af98467] {
      width: 200px;
      height: 350px
    }

    * {
      margin: 0;
      -webkit-tap-highlight-color: transparent
    }

    html,
    body {
      -webkit-user-select: none;
      user-select: none;
      width: 100%;
      height: 100%
    }

    body {
      overflow-x: hidden;
      font-size: 16px
    }

    uni-app,
    uni-page,
    uni-page-wrapper,
    uni-page-body {
      display: block;
      box-sizing: border-box;
      width: 100%
    }

    uni-page-wrapper {
      position: relative
    }

    #app,
    uni-app,
    uni-page,
    uni-page-wrapper {
      height: 100%
    }

    .uni-mask {
      position: fixed;
      z-index: 999;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      background: rgba(0, 0, 0, .5)
    }

    .uni-fade-enter-active,
    .uni-fade-leave-active {
      transition-duration: .25s;
      transition-property: opacity;
      transition-timing-function: ease
    }

    .uni-fade-enter-from,
    .uni-fade-leave-active {
      opacity: 0
    }

  

    .uni-loading {
      width: 20px;
      height: 20px;
      display: inline-block;
      vertical-align: middle;
      animation: uni-loading 1s steps(12, end) infinite;
      background-size: 100%
    }

    @keyframes uni-loading {
      0% {
        transform: rotate3d(0, 0, 1, 0)
      }

      to {
        transform: rotate3d(0, 0, 1, 360deg)
      }
    }

    html {
      --primary-color: #007aff;
      --UI-BG: #fff;
      --UI-BG-1: #f7f7f7;
      --UI-BG-2: #fff;
      --UI-BG-3: #f7f7f7;
      --UI-BG-4: #4c4c4c;
      --UI-BG-5: #fff;
      --UI-FG: #000;
      --UI-FG-0: rgba(0, 0, 0, .9);
      --UI-FG-HALF: rgba(0, 0, 0, .9);
      --UI-FG-1: rgba(0, 0, 0, .5);
      --UI-FG-2: rgba(0, 0, 0, .3);
      --UI-FG-3: rgba(0, 0, 0, .1)
    }

    body:after {
      position: fixed;
      content: "";
      left: -1000px;
      top: -1000px;
      animation: shadow-preload .1s;
      animation-delay: 3s
    }

    @keyframes shadow-preload {
      0% {
        background-image:
          /*savepage-url=https://cdn.dcloud.net.cn/img/shadow-grey.png*/
          url()
      }

      to {
        background-image:
          /*savepage-url=https://cdn.dcloud.net.cn/img/shadow-grey.png*/
          url()
      }
    }

    .uni-async-error {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      color: #999;
      padding: 100px 10px;
      text-align: center
    }

    .uni-async-loading {
      box-sizing: border-box;
      width: 100%;
      padding: 50px;
      text-align: center
    }

    .uni-async-loading .uni-loading {
      width: 30px;
      height: 30px
    }

    uni-content {
      display: flex;
      flex: 1 0 auto;
      height: 100%
    }

    uni-main {
      flex: 1;
      width: 100%
    }

    uni-top-window+uni-content {
      height: calc(100vh - var(--top-window-height))
    }

    uni-left-window {
      position: relative;
      width: var(--window-left);
      order: -1;
      overflow-x: hidden
    }

    uni-right-window {
      position: relative;
      width: var(--window-right);
      overflow-x: hidden
    }

    uni-left-window[data-show],
    uni-right-window[data-show] {
      position: absolute
    }

    uni-right-window[data-show] {
      right: 0
    }

    uni-content .uni-mask,
    .uni-left-window,
    .uni-right-window {
      z-index: 997
    }

    .uni-mask+.uni-left-window,
    .uni-mask+.uni-right-window {
      position: fixed
    }

    .uni-top-window {
      position: fixed;
      left: var(--window-margin);
      right: var(--window-margin);
      top: 0;
      z-index: 998;
      overflow: hidden
    }

    uni-page-head {
      display: block;
      box-sizing: border-box
    }

    .uni-page-head {
      position: fixed;
      left: var(--window-left);
      right: var(--window-right);
      height: 44px;
      height: calc(44px + constant(safe-area-inset-top));
      height: calc(44px + env(safe-area-inset-top));
      padding: 7px 3px;
      padding-top: calc(7px + constant(safe-area-inset-top));
      padding-top: calc(7px + env(safe-area-inset-top));
      display: flex;
      overflow: hidden;
      justify-content: space-between;
      box-sizing: border-box;
      z-index: 998;
      color: #fff;
      background-color: #000;
      transition-property: all
    }

    .uni-page-head * {
      box-sizing: border-box
    }

    .uni-page-head .uni-btn-icon {
      overflow: hidden;
      min-width: 1em;
      font-style: normal
    }

    .uni-page-head-titlePenetrate,
    .uni-page-head-titlePenetrate .uni-page-head-bd,
    .uni-page-head-titlePenetrate .uni-page-head-bd * {
      pointer-events: none
    }

    .uni-page-head-titlePenetrate * {
      pointer-events: auto
    }

    .uni-page-head.uni-page-head-transparent .uni-page-head-ft>div {
      justify-content: center
    }

    .uni-page-head~.uni-placeholder {
      width: 100%;
      height: 44px;
      height: calc(44px + constant(safe-area-inset-top));
      height: calc(44px + env(safe-area-inset-top))
    }

    .uni-placeholder-titlePenetrate {
      pointer-events: none
    }

    .uni-page-head-hd {
      display: flex;
      align-items: center;
      font-size: 16px
    }

    .uni-page-head-bd {
      position: absolute;
      left: 70px;
      right: 70px;
      min-width: 0;
      -webkit-user-select: auto;
      user-select: auto
    }

    .uni-page-head-btn {
      position: relative;
      width: auto;
      margin: 0 2px;
      word-break: keep-all;
      white-space: pre;
      cursor: pointer;
      font-size: 0px
    }

    .uni-page-head-transparent .uni-page-head-btn {
      display: flex;
      align-items: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, .5)
    }

    .uni-page-head-btn-red-dot:after {
      content: attr(badge-text);
      position: absolute;
      right: 0;
      top: 0;
      background-color: red;
      color: #fff;
      width: 18px;
      height: 18px;
      line-height: 18px;
      border-radius: 18px;
      overflow: hidden;
      transform: scale(.5) translate(40%, -40%);
      transform-origin: 100% 0
    }

    .uni-page-head-btn-red-dot[badge-text]:after {
      font-size: 12px;
      width: auto;
      min-width: 18px;
      max-width: 42px;
      text-align: center;
      padding: 0 3px;
      transform: scale(.7) translate(40%, -40%)
    }

    .uni-page-head-btn-select svg {
      vertical-align: middle;
      margin-left: 2px;
      transform: rotate(270deg) scale(.8)
    }

    .uni-page-head-search {
      position: relative;
      display: flex;
      flex: 1;
      margin: 0 2px;
      line-height: 30px;
      font-size: 15px
    }

    .uni-page-head-search-input {
      width: 100%;
      height: 100%;
      padding-left: 34px;
      text-align: left
    }

    .uni-page-head-search-input .uni-input-input:disabled {
      pointer-events: none
    }

    .uni-page-head-search-placeholder {
      position: absolute;
      max-width: 100%;
      height: 100%;
      padding-left: 34px;
      overflow: hidden;
      word-break: keep-all;
      white-space: pre
    }

    .uni-page-head-search-placeholder-right {
      right: 0
    }

    .uni-page-head-search-placeholder-center {
      left: 50%;
      transform: translate(-50%)
    }

    .uni-page-head-search-icon {
      position: absolute;
      top: 0;
      left: 2px;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center
    }

    .uni-page-head-ft {
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      font-size: 13px
    }

    .uni-page-head__title {
      font-weight: 700;
      font-size: 16px;
      line-height: 30px;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis
    }

    .uni-page-head__title .uni-loading {
      width: 16px;
      height: 16px;
      margin-top: -3px
    }

    .uni-page-head__title .uni-page-head__title_image {
      width: auto;
      height: 26px;
      vertical-align: middle
    }

    .uni-page-head-shadow {
      overflow: visible
    }

    .uni-page-head-shadow:after {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      top: 100%;
      height: 5px;
      background-size: 100% 100%
    }

    uni-page-head[uni-page-head-type=default]~uni-page-wrapper {
      height: calc(100% - 44px);
      height: calc(100% - 44px - constant(safe-area-inset-top));
      height: calc(100% - 44px - env(safe-area-inset-top))
    }

    .uni-page-head-shadow-grey:after {
      background-image:
        /*savepage-url=https://cdn.dcloud.net.cn/img/shadow-grey.png*/
        url()
    }

    .uni-page-head-shadow-blue:after {
      background-image:
        /*savepage-url=https://cdn.dcloud.net.cn/img/shadow-blue.png*/
        url()
    }

    .uni-page-head-shadow-green:after {
      background-image:
        /*savepage-url=https://cdn.dcloud.net.cn/img/shadow-green.png*/
        url()
    }

    .uni-page-head-shadow-orange:after {
      background-image:
        /*savepage-url=https://cdn.dcloud.net.cn/img/shadow-orange.png*/
        url()
    }

    .uni-page-head-shadow-red:after {
      background-image:
        /*savepage-url=https://cdn.dcloud.net.cn/img/shadow-red.png*/
        url()
    }

    .uni-page-head-shadow-yellow:after {
      background-image:
        /*savepage-url=https://cdn.dcloud.net.cn/img/shadow-yellow.png*/
        url()
    }

    uni-tabbar {
      display: block;
      box-sizing: border-box;
      width: 100%;
      z-index: 998
    }

    .uni-tabbar {
      display: flex;
      z-index: 998;
      box-sizing: border-box
    }

    .uni-tabbar-top,
    .uni-tabbar-bottom,
    .uni-tabbar-top .uni-tabbar,
    .uni-tabbar-bottom .uni-tabbar {
      position: fixed;
      left: var(--window-left);
      right: var(--window-right)
    }

    .uni-app--showlayout+.uni-tabbar-top,
    .uni-app--showlayout+.uni-tabbar-bottom,
    .uni-app--showlayout+.uni-tabbar-top .uni-tabbar,
    .uni-app--showlayout+.uni-tabbar-bottom .uni-tabbar {
      left: var(--window-margin);
      right: var(--window-margin)
    }

    .uni-tabbar-bottom .uni-tabbar {
      bottom: 0;
      padding-bottom: 0;
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom)
    }

    .uni-tabbar~.uni-placeholder {
      width: 100%;
      margin-bottom: 0;
      margin-bottom: constant(safe-area-inset-bottom);
      margin-bottom: env(safe-area-inset-bottom)
    }

    .uni-tabbar * {
      box-sizing: border-box
    }

    .uni-tabbar__item {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      flex: 1;
      font-size: 0;
      text-align: center;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
    }

    .uni-tabbar__bd {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer
    }

    .uni-tabbar__icon {
      position: relative;
      display: inline-block;
      margin-top: 5px
    }

    .uni-tabbar__icon.uni-tabbar__icon__diff {
      margin-top: 0;
      width: 34px;
      height: 34px
    }

    .uni-tabbar__icon img {
      width: 100%;
      height: 100%
    }

    .uni-tabbar__iconfont {
      font-family: UniTabbarIconFont
    }

    .uni-tabbar__label {
      position: relative;
      text-align: center;
      font-size: 10px
    }

    .uni-tabbar-border {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 1px;
      transform: scaleY(.5)
    }

    .uni-tabbar__reddot {
      position: absolute;
      top: 2px;
      right: 0;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #f43530;
      color: #fff;
      transform: translate(40%)
    }

    .uni-tabbar__badge {
      width: auto;
      height: 16px;
      line-height: 16px;
      border-radius: 16px;
      min-width: 16px;
      padding: 0 2px;
      font-size: 12px;
      text-align: center;
      white-space: nowrap
    }

    .uni-tabbar__mid {
      display: flex;
      justify-content: center;
      position: absolute;
      bottom: 0;
      background-size: 100% 100%
    }

    .uni-app--showtabbar uni-page-wrapper {
      display: block;
      height: calc(100% - var(--tab-bar-height));
      height: calc(100% - var(--tab-bar-height) - constant(safe-area-inset-bottom));
      height: calc(100% - var(--tab-bar-height) - env(safe-area-inset-bottom))
    }

    uni-page[data-type] uni-page-wrapper {
      height: 100%
    }

    .uni-app--showtabbar uni-page-wrapper:after {
      content: "";
      display: block;
      width: 100%;
      height: var(--tab-bar-height);
      height: calc(var(--tab-bar-height) + constant(safe-area-inset-bottom));
      height: calc(var(--tab-bar-height) + env(safe-area-inset-bottom))
    }

    .uni-app--showtabbar uni-page-head[uni-page-head-type=default]~uni-page-wrapper {
      height: calc(100% - 44px - var(--tab-bar-height));
      height: calc(100% - 44px - constant(safe-area-inset-top) - var(--tab-bar-height) - constant(safe-area-inset-bottom));
      height: calc(100% - 44px - env(safe-area-inset-top) - var(--tab-bar-height) - env(safe-area-inset-bottom))
    }

    :root {
      color-scheme: dark;
      --color-unique: #bd9b4e;
      --color-magic: #84b7ff;
      --color-rare: #ffff00;
      --color-set: #00ff00;
      --color-runeword: #e9c964;
      --color-rune: #ee7a03;
      --color-craft: #fab100;
      --color-socket: #888;
      --color-normal: #d8d8d8;
      --color-set-bonus: #cebc86;
      --color-red: #ff5353;
      --color-terror: #bf23bf;
      --d4-color-label: #ad9d8e;
      --d4-color-resource: #e3e79d;
      --d4-color-number: #ffc772;
      --d4-color-important: #e3e0d9;
      --d4-color-green: #43d630;
      --d4-color-heart: #c3fffa;
      --d4-color-summon: #5fc3f1;
      --d4-color-mythic: #cda1d8;
      --d4-color-unique: #f7c99c;
      --d4-color-legendary: #f8930a;
      --d4-color-rare: #f1f25c;
      --d4-color-magic: #6d9eeb;
      --d4-color-normal: #ffffff;
      --poe2-color-gem: #1ba29b;
      --poe2-color-tooltip: #7f7f7f;
      --color-pink: #af166a;
      --color-primary: #e9c964;
      --color-bg: #1b1b1b;
      --color-bg-light: #212121;
      --color-bg-lightest: #333333;
      --color-bg-dark: #121212;
      --color-tooltip: #bab0a3;
      --color-text-normal: #c0c0c0;
      --color-text-grey: #8a8a8a;
      --color-text-title: #e3e3e3;
      --font-size-xl: 1.125rem;
      --font-size-large: 1rem;
      --font-size-normal: .875rem;
      --font-size-small: .75rem;
      --font-size-xs: .625rem;
      --color-border: #3b3b3b;
      --color-control-border: #242424;
      --safe-area-inset-top: 0;
      --safe-area-inset-top: constant(safe-area-inset-top);
      --safe-area-inset-top: env(safe-area-inset-top);
      --safe-area-inset-bottom: 0;
      --safe-area-inset-bottom: constant(safe-area-inset-bottom);
      --safe-area-inset-bottom: env(safe-area-inset-bottom);
      --padding-hoz: .75rem
    }

    :root:root {
      --van-red: var(--color-red);
      --van-padding-md: .75rem;
      --van-overlay-background: rgba(0, 0, 0, .8);
      --van-popover-dark-background: var(--color-bg);
      --van-popover-radius: 0;
      --van-dialog-background: var(--color-bg);
      --van-text-color: var(--color-text-normal);
      --van-button-default-background: transparent;
      --van-border-color: var(--color-bg-lightest);
      --van-dialog-has-title-message-text-color: var(--color-text-normal);
      --van-primary-color: var(--color-primary);
      --van-dropdown-menu-title-font-size: var(--font-size-normal);
      --van-font-size-md: var(--font-size-normal);
      --van-dialog-radius: .375rem;
      --van-background-2: var(--color-bg-dark);
      --van-text-color-3: var(--color-text-grey);
      --van-radius-lg: .25rem;
      --van-cell-group-inset-padding: 0;
      --van-field-error-message-font-size: .875rem;
      --van-active-color: var(--color-bg-lightest);
      --van-field-label-color: var(--d4-color-label);
      --van-notice-bar-background: var(--color-bg-dark);
      --van-notice-bar-text-color: var(--color-primary);
      --van-radio-size: 16px;
      --van-checkbox-size: 16px;
      --van-pagination-background: trasparent;
      --van-pagination-height: 30px;
      --van-pagination-item-disabled-background: var(--color-bg-lightest);
      --van-switch-node-background: var(--color-text-normal);
      --van-popover-action-height: 36px;
      --van-popover-action-width: 100px;
      --van-popover-action-font-size: .875rem;
      --van-image-placeholder-background: transparent;
      --van-image-error-icon-color: transparent;
      --van-radio-disabled-background: #a8a8a8;
      --van-stepper-button-disabled-color: var(--color-bg-dark)
    }

    .game-tli {
      --d4-color-unique: #d86a23;
      --d4-color-legendary: #d86a23;
      --d4-color-magic: #1fa5da;
      --d4-color-rare: #921ab4;
      --d4-color-number: #0f0;
      --d4-color-important: #00faf9;
      --window-top: calc(80px + env(safe-area-inset-top))
    }

    .game-le {
      --color-bg-dark: #100e0e;
      --color-control-border: #302b10
    }

    .game-poe2 {
      --unique-color: #af6025;
      --divination-color: #0ebaff;
      --prophecy-color: #b54bff;
      --quest-color: #4ae63a;
      --currency-color: #aa9e82;
      --gem-color: #1ba29b;
      --cold-color: #366492;
      --afflictionmod-color: #16191c;
      --heist-color: #191e19;
      --lightning-color: #ffd700;
      --magic-color: #8888ff;
      --rare-color: #ffff77;
      --green-color: #46a239;
      --chaos-color: #d02090;
      --fire-color: #960000;
      --title-color: #e7b478;
      --legacy-color: #d4913f;
      --normal-color: #c8c8c8;
      --corrupted-color: #d20000;
      --scourged-color: #ff6e25;
      --default-color: #7f7f7f;
      --fractured-color: #a29162;
      --crafted-color: #b4b4ff;
      --dark-color: #0f0f0f;
      --craftaffectwarning-color: #ebc850;
      --coolgrey-700: 209, 18%, 30%;
      --yellow-300: 50, 98%, 64%;
      --color-tooltip: rgb(127, 127, 127);
      --d4-color-number: #ffffff;
      --d4-color-magic: var(--magic-color);
      --d4-color-label: #baad85
    }

    @media (min-width: 1024px) {
      :root {
        --font-size-small: .875rem;
        --font-size-normal: 1rem;
        --font-size-large: 1.25rem;
        --font-size-xl: 1.5rem
      }

      .game-d4 {
        --window-top: calc(80px + env(safe-area-inset-top))
      }
    }

    .text-normal {
      line-height: 1.5;
      font-size: var(--font-size-normal);
      color: var(--color-text-normal)
    }

    .text-grey {
      line-height: 1.5;
      font-size: var(--font-size-normal);
      color: var(--color-text-grey)
    }

    .text-attr {
      line-height: 1.5;
      font-size: var(--font-size-normal);
      color: var(--color-magic)
    }

    .text-title {
      font-size: var(--font-size-large);
      color: var(--color-unique);
      font-weight: 700
    }

    .text-avqest {
      font-family: Av Qest;
      font-size: var(--font-size-xl);
      color: var(--color-unique)
    }

    .font-bold {
      font-weight: 700
    }

    .color-unique {
      color: var(--color-unique)
    }

    .color-magic {
      color: var(--color-magic)
    }

    .color-rare {
      color: var(--color-rare)
    }

    .color-set {
      color: var(--color-set)
    }

    .color-runeword {
      color: var(--color-runeword)
    }

    .color-rune {
      color: var(--color-rune)
    }

    .color-craft {
      color: var(--color-craft)
    }

    .color-socket {
      color: var(--color-socket)
    }

    .color-normal {
      color: var(--color-normal)
    }

    .color-set-bonus {
      color: var(--color-set-bonus)
    }

    .color-red {
      color: var(--color-red)
    }

    .color-text-grey {
      color: var(--color-text-grey)
    }

    .color-terror {
      color: var(--color-terror)
    }

    .mgb-12 {
      margin-bottom: .375rem !important
    }

    .mgb-16 {
      margin-bottom: .5rem !important
    }

    .mgb-20 {
      margin-bottom: .625rem !important
    }

    .mgb-24 {
      margin-bottom: .75rem !important
    }

    .mgt-12 {
      margin-top: .375rem !important
    }

    .mgt-16 {
      margin-top: .5rem !important
    }

    .mgt-20 {
      margin-top: .625rem !important
    }

    .mgt-24 {
      margin-top: .75rem !important
    }

    .mgl-12 {
      margin-left: .375rem !important
    }

    .mgl-16 {
      margin-left: .5rem !important
    }

    .mgl-20 {
      margin-left: .625rem !important
    }

    .mgl-24 {
      margin-left: .75rem !important
    }

    .mgr-12 {
      margin-right: .375rem !important
    }

    .mgr-16 {
      margin-right: .5rem !important
    }

    .mgr-20 {
      margin-right: .625rem !important
    }

    .mgr-24 {
      margin-right: .75rem !important
    }

    .text-align-center {
      text-align: center
    }

    .text-align-left {
      text-align: left
    }

    .text-align-right {
      text-align: right
    }

    .row {
      flex-direction: row !important;
      align-items: center
    }

    .pageTitle {
      margin: 16px 0 !important;
      font-size: 22px;
      color: var(--color-runeword)
    }

    .d4-color-unique,
    .d4-color-uniqueItem {
      color: var(--d4-color-unique)
    }

    .d4-color-legendary {
      color: var(--d4-color-legendary)
    }

    .d4-color-rare {
      color: var(--d4-color-rare)
    }

    .d4-color-normal {
      color: var(--d4-color-normal)
    }

    .d4-color-magic {
      color: var(--d4-color-magic)
    }

    .d4-color-label {
      color: var(--d4-color-label)
    }

    .d4-color-pink {
      color: var(--color-pink)
    }

    .d4-color-heart {
      color: var(--d4-color-heart)
    }

    .d4-color-summon {
      color: var(--d4-color-summon)
    }

    .d4-color-mythic {
      color: var(--d4-color-mythic)
    }

    .d4-color-rune {
      color: var(--color-rune)
    }

    .d4-frame-rare,
    .d4-frame-legendary,
    .d4-frame-uniqueItem,
    .d4-frame-unique,
    .d4-frame-mythic {
      background-image:
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/data_img/d4/ui/item-frame-2.webp*/
        url();
      background-size: 700% 100%
    }

    .d4-frame-rare {
      background-position-x: -200%
    }

    .d4-frame-legendary {
      background-position-x: -300%
    }

    .d4-frame-uniqueItem,
    .d4-frame-unique {
      background-position-x: -400%
    }

    .d4-frame-mythic {
      background-position-x: -600%
    }

    .d4-dot-list-item {
      padding-left: 1.25rem;
      position: relative
    }

    .d4-dot-list-item:before {
      display: block;
      width: .125rem;
      height: .125rem;
      border: .125rem solid var(--color-text-grey);
      content: " ";
      position: absolute;
      left: .375rem;
      top: .625rem;
      margin-top: -.125rem;
      transform: rotate(-45deg)
    }

    .d2core-tooltip {
      max-width: calc(100vw - 24px);
      margin: 16px;
      border-image:
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/data_img/d4/ui/tooltip-base.webp*/
        var(--savepage-url-66) 76 fill/38px stretch;
      z-index: 0;
      position: relative
    }

    .d2core-tooltip .database-item-line:last-child {
      display: none
    }

    .d2core-tooltip .tooltip-tip {
      padding: .75rem
    }

    .d2core-tooltip.tooltip-tli {
      border-image: none
    }

    .d2core-tooltip.tooltip-le {
      border-image: none;
      margin: 0
    }

    .d2core-tooltip.tooltip-poe2 {
      border-image: none
    }

    .tooltip-wrapper {
      --van-popup-background: transparent;
      --van-popover-light-background: transparent;
      overflow-y: visible !important
    }

    .tooltip-wrapper .van-popover__content {
      overflow: visible
    }

    .tli-class-icon {
      width: 1em;
      height: 1em;
      background:
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/data_img/tli/ui/classes-ss6.webp*/
        url();
      background-size: auto 1em;
      border: 1px solid #999;
      border-top-left-radius: .25em;
      border-bottom-right-radius: .25em
    }

    .tli-class-icon.class-1001 {
      background-position-x: -9em
    }

    .tli-class-icon.class-1002 {
      background-position-x: -10em
    }

    .tli-class-icon.class-2001 {
      background-position-x: -1em
    }

    .tli-class-icon.class-2002 {
      background-position-x: -2em
    }

    .tli-class-icon.class-2003 {
      background-position-x: -18em
    }

    .tli-class-icon.class-3001 {
      background-position-x: -3em
    }

    .tli-class-icon.class-3002 {
      background-position-x: -4em
    }

    .tli-class-icon.class-4001 {
      background-position-x: 0
    }

    .tli-class-icon.class-5000 {
      background-position-x: -15em
    }

    .tli-class-icon.class-5001 {
      background-position-x: -6em
    }

    .tli-class-icon.class-5002 {
      background-position-x: -5em
    }

    .tli-class-icon.class-7001 {
      background-position-x: -11em
    }

    .tli-class-icon.class-7002 {
      background-position-x: -14em
    }

    .tli-class-icon.class-8001 {
      background-position-x: -12em
    }

    .tli-class-icon.class-8002 {
      background-position-x: -13em
    }

    .tli-class-icon.class-9001 {
      background-position-x: -7em
    }

    .tli-class-icon.class-9002 {
      background-position-x: -8em
    }

    .tli-class-icon.class-15001 {
      background-position-x: -17em
    }

    .tli-class-icon.class-16001 {
      background-position-x: -16em
    }

    .tli-class-icon.class-17001 {
      background-position-x: -19em
    }

    .tli-item-frame {
      border-bottom: 4px solid #ededed;
      border-top-right-radius: 19px;
      border-bottom-left-radius: 19px;
      box-shadow: 1px -1px 1px #1b1b1d;
      position: relative;
      overflow: hidden
    }

    .tli-item-frame.shadow:before {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      top: 50%;
      background: linear-gradient(to top, #ededed, transparent);
      z-index: 1
    }

    .tli-item-frame.frame-legendary {
      background: linear-gradient(to bottom, #232323, #d86a23);
      box-shadow: 0 10px 16px rgba(216, 106, 35, .3), 1px -1px 1px #1b1b1d;
      border-color: #febc69
    }

    .tli-item-frame.frame-legendary.shadow:before {
      background: linear-gradient(to top, #d86a23, transparent)
    }

    .tli-item-frame.frame-magic {
      background: linear-gradient(to bottom, #232323, #1fa5da);
      box-shadow: 0 10px 16px rgba(31, 165, 218, .3), 1px -1px 1px #1b1b1d;
      border-color: #80e6ff
    }

    .tli-item-frame.frame-magic.shadow:before {
      background: linear-gradient(to top, #1fa5da, transparent)
    }

    .tli-item-frame.frame-rare {
      background: linear-gradient(to bottom, #232323, #921ab4);
      box-shadow: 0 10px 16px rgba(146, 26, 180, .3), 1px -1px 1px #1b1b1d;
      border-color: #dd8cff
    }

    .tli-item-frame.frame-rare.shadow:before {
      background: linear-gradient(to top, #921ab4, transparent)
    }

    .tli-item-frame.frame-pink {
      background: linear-gradient(to bottom, #232323, #af166a);
      box-shadow: 0 10px 16px rgba(175, 22, 106, .3), 1px -1px 1px #1b1b1d;
      border-color: #ff87c4
    }

    .tli-item-frame.frame-pink.shadow:before {
      background: linear-gradient(to top, #af166a, transparent)
    }

    .game-tli .d4-frame-unique {
      background: linear-gradient(180deg, #232323, #d86a23)
    }

    .game-tli .d4-frame-normal {
      background: linear-gradient(180deg, #232323, #b7b7b7)
    }

    .game-tli .d4-frame-magic {
      background: linear-gradient(180deg, #232323, #1fa5da)
    }

    .game-tli .d4-frame-rare {
      background: linear-gradient(180deg, #232323, #921ab4)
    }

    .game-le .d4-frame-unique {
      background: rgba(25, 12, 1, .97) !important;
      border: 1px solid rgba(106, 52, 4, .89)
    }

    .game-le .d4-frame-set {
      background: rgba(12, 24, 13, .97) !important;
      border: 1px solid rgba(51, 104, 56, .89)
    }

    .le-class-icon {
      width: 1em;
      height: 1em;
      background:
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/data_img/le/ui/classes.webp*/
        url();
      background-size: 4em auto
    }

    .le-class-icon.class-0-0 {
      background-position: 0 -3em
    }

    .le-class-icon.class-0-1 {
      background-position: -3em -3em
    }

    .le-class-icon.class-0-2 {
      background-position: -1em -3em
    }

    .le-class-icon.class-0-3 {
      background-position: -2em -3em
    }

    .le-class-icon.class-1-0 {
      background-position: 0 -4em
    }

    .le-class-icon.class-1-1 {
      background-position: -2em -4em
    }

    .le-class-icon.class-1-2 {
      background-position: -1em -4em
    }

    .le-class-icon.class-1-3 {
      background-position: -3em -4em
    }

    .le-class-icon.class-2-0 {
      background-position: 0 0
    }

    .le-class-icon.class-2-1 {
      background-position: -1em 0
    }

    .le-class-icon.class-2-2 {
      background-position: -3em 0
    }

    .le-class-icon.class-2-3 {
      background-position: -2em 0
    }

    .le-class-icon.class-3-0 {
      background-position: 0 -1em
    }

    .le-class-icon.class-3-1 {
      background-position: -2em -1em
    }

    .le-class-icon.class-3-2 {
      background-position: -3em -1em
    }

    .le-class-icon.class-3-3 {
      background-position: -1em -1em
    }

    .le-class-icon.class-4-0 {
      background-position: 0 -2em
    }

    .le-class-icon.class-4-1 {
      background-position: -3em -2em
    }

    .le-class-icon.class-4-2 {
      background-position: -1em -2em
    }

    .le-class-icon.class-4-3 {
      background-position: -2em -2em
    }

    .le-ability-icon {
      width: 64px;
      height: 64px;
      border: 1px solid #2196f3
    }

    * {
      box-sizing: border-box
    }

    .page-container {
      background: var(--color-bg);
      box-shadow: 0 0 5px rgba(0, 0, 0, .75);
      min-height: 100vh
    }

    .page-container:after {
      display: table;
      content: " "
    }

    .control {
      background-color: var(--color-bg-dark);
      border: 2px solid var(--color-control-border);
      border-radius: 0;
      box-shadow: 1px 1px 1px rgba(0, 0, 0, .8)
    }

    .w-e-text-container [data-slate-editor] blockquote {
      background-color: var(--color-bg-lightest);
      border-left: 4px solid var(--color-primary)
    }

    .van-checkbox,
    .van-radio {
      overflow: visible !important
    }

    .van-popover__content {
      overflow: visible
    }

    .van-field__control {
      font-size: var(--font-size-normal);
      font-family: inherit
    }

    .van-checkbox__icon--checked .van-icon {
      color: #000 !important
    }

    .van-radio-group:not(.van-radio-group--horizontal) .van-radio {
      margin-bottom: .375rem
    }

    .van-radio-group:not(.van-radio-group--horizontal) .van-radio:last-child {
      margin-bottom: 0
    }

    .van-checkbox--horizontal {
      margin-bottom: .375rem
    }

    .van-radio__icon--checked .van-icon {
      color: #000 !important
    }

    .van-pagination__item.van-pagination__item--active {
      background-color: transparent
    }

    .van-pagination__item.van-pagination__item--active>* {
      color: var(--color-text-normal)
    }

    .van-pagination__item:active {
      background-color: var(--color-bg-lightest)
    }

    .van-pagination__page-desc {
      flex: 0 0 auto;
      min-width: 60px
    }

    .van-pagination__item>* {
      color: var(--color-text-grey);
      background-color: transparent;
      -webkit-appearance: none;
      appearance: none;
      outline: none;
      border: none;
      cursor: pointer;
      height: 100%;
      flex: 1
    }

    .van-switch--on .van-switch__node {
      background-color: var(--color-bg)
    }

    .van-dialog .van-dialog__header {
      color: var(--color-primary)
    }

    @font-face {
      font-family: Av Qest;
      src:
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/AvQest.ttf*/
        url();
      font-weight: 400;
      font-style: normal;
      /*savepage-font-display=swap*/
    }

    @font-face {
      font-family: Exo2;
      src:
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/font/Exo2.ttf*/
        url();
      font-weight: 400;
      font-style: normal;
      /*savepage-font-display=swap*/
    }

    html,
    body {
      -webkit-user-select: auto !important;
      user-select: auto !important
    }

    body {
      background-image: radial-gradient(42.01% 31.98% at 50% 29.97%, rgba(0, 0, 0, .75), rgba(0, 0, 0, 0)), linear-gradient(90deg, black 0%, transparent 50%, black 100%),
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/d4-season7-bg.webp?imageView2/2/w/1800/format/webp/q/80*/
        var(--savepage-url-15);
      background-repeat: no-repeat;
      background-position: center 0;
      background-attachment: fixed;
      background-color: #000;
      font-family: Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC, Microsoft YaHei;
      color: var(--color-text-normal);
      font-size: var(--font-size-normal)
    }

    body.wx-web {
      --window-top: 0
    }

    body.wx-web uni-page-head {
      display: none
    }

    body.game-tli {
      background: linear-gradient(rgba(0, 0, 0, .8), rgba(0, 0, 0, .8)),
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/data_img/tli/image_card/ss6-bg.webp*/
        url() no-repeat center center;
      background-size: cover
    }

    body.game-le {
      background-image:
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/data_img/le/image_card/s2-bg.jpg*/
        url();
      background-size: cover
    }

    body.game-poe2 {
      background-image:
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/data_img/poe2/bg.webp*/
        url();
      background-size: cover
    }

    a {
      color: var(--color-primary)
    }

    uni-button {
      background-color: transparent;
      -webkit-appearance: none;
      appearance: none;
      outline: none;
      border: none
    }

    uni-app {
      position: relative
    }

    .uni-top-window--placeholder {
      height: 92px !important
    }

    .uni-top-window {
      overflow: visible !important
    }

    .tooltip-link {
      color: var(--color-primary)
    }

    .uni-page-head {
      top: 0
    }

    uni-page-body {
      min-height: 100vh;
      color: var(--color-text-normal)
    }

    uni-toast {
      z-index: 9999
    }

    ins.adsbygoogle[data-ad-status=unfilled] {
      display: none !important
    }

    @media (min-width: 1024px) {
      body {
        --top-window-height: 92px
      }

      html,
      body {
        height: auto
      }

      br {
        content: "";
        margin: 2em;
        display: block;
        font-size: 24%
      }

      uni-top-window+uni-content {
        height: auto
      }

      uni-page-body {
        margin-bottom: 24px;
        min-height: calc(100vh - var(--top-window-height) - 28px);
        position: relative
      }

      uni-page-head {
        display: none
      }

      .page-container {
        min-height: calc(100vh - var(--top-window-height) - 28px)
      }

      .uni-tabbar-bottom {
        display: none
      }

      .control.control-button {
        cursor: pointer
      }

      .control.control-button:hover {
        border-color: var(--color-primary)
      }

      .control-scrollbar::-webkit-scrollbar {
        width: 8px;
        height: 8px
      }

      .control-scrollbar::-webkit-scrollbar-thumb {
        background-color: var(--color-unique)
      }

      .van-checkbox--horizontal {
        margin-bottom: 0
      }
    }

    

   

    .d2core-list:before {
      content: ""
    }

    .d2core-grid:before {
      content: ""
    }

    .d2core-copy:before {
      content: ""
    }

    .d2core-flash:before {
      content: ""
    }

    .d2core-save:before {
      content: ""
    }

    .d2core-old-user:before {
      content: ""
    }

    .d2core-merchant:before {
      content: ""
    }

    .d2core-server-slam:before {
      content: ""
    }

    .d2core-donate:before {
      content: ""
    }

    .d2core-money-rmb:before {
      content: ""
    }

    .d2core-order-success:before {
      content: ""
    }

    .d2core-shopping:before {
      content: ""
    }

    .d2core-battle-net:before {
      content: ""
    }

    .d2core-nintendoswitch:before {
      content: ""
    }

    .d2core-xbox:before {
      content: ""
    }

    .d2core-playstation:before {
      content: ""
    }

    .d2core-shut-down:before {
      content: ""
    }

    .d2core-fullscreen:before {
      content: ""
    }

    .d2core-fullscreen-exit:before {
      content: ""
    }

    .d2core-sum:before {
      content: ""
    }

    .d2core-sorting:before {
      content: ""
    }

    .d2core-filter-fill:before {
      content: ""
    }

    .d2core-filter:before {
      content: ""
    }

    .d2core-global:before {
      content: ""
    }

    :root {
      --van-black: #000;
      --van-white: #fff;
      --van-gray-1: #f7f8fa;
      --van-gray-2: #f2f3f5;
      --van-gray-3: #ebedf0;
      --van-gray-4: #dcdee0;
      --van-gray-5: #c8c9cc;
      --van-gray-6: #969799;
      --van-gray-7: #646566;
      --van-gray-8: #323233;
      --van-red: #ee0a24;
      --van-blue: #1989fa;
      --van-orange: #ff976a;
      --van-orange-dark: #ed6a0c;
      --van-orange-light: #fffbe8;
      --van-green: #07c160;
      --van-gradient-red: linear-gradient(to right, #ff6034, #ee0a24);
      --van-gradient-orange: linear-gradient(to right, #ffd01e, #ff8917);
      --van-primary-color: var(--van-blue);
      --van-success-color: var(--van-green);
      --van-danger-color: var(--van-red);
      --van-warning-color: var(--van-orange);
      --van-text-color: var(--van-gray-8);
      --van-text-color-2: var(--van-gray-6);
      --van-text-color-3: var(--van-gray-5);
      --van-active-color: var(--van-gray-2);
      --van-active-opacity: .6;
      --van-disabled-opacity: .5;
      --van-background: var(--van-gray-1);
      --van-background-2: var(--van-white);
      --van-background-3: var(--van-white);
      --van-padding-base: 4px;
      --van-padding-xs: 8px;
      --van-padding-sm: 12px;
      --van-padding-md: 16px;
      --van-padding-lg: 24px;
      --van-padding-xl: 32px;
      --van-font-bold: 600;
      --van-font-size-xs: 10px;
      --van-font-size-sm: 12px;
      --van-font-size-md: 14px;
      --van-font-size-lg: 16px;
      --van-line-height-xs: 14px;
      --van-line-height-sm: 18px;
      --van-line-height-md: 20px;
      --van-line-height-lg: 22px;
      --van-base-font: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Segoe UI, Arial, Roboto, "PingFang SC", "miui", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
      --van-price-font: avenir-heavy, "PingFang SC", helvetica neue, arial, sans-serif;
      --van-duration-base: .3s;
      --van-duration-fast: .2s;
      --van-ease-out: ease-out;
      --van-ease-in: ease-in;
      --van-border-color: var(--van-gray-3);
      --van-border-width: 1px;
      --van-radius-sm: 2px;
      --van-radius-md: 4px;
      --van-radius-lg: 8px;
      --van-radius-max: 999px
    }

    .van-theme-dark {
      --van-text-color: #f5f5f5;
      --van-text-color-2: #707070;
      --van-text-color-3: #4d4d4d;
      --van-border-color: #3a3a3c;
      --van-active-color: #3a3a3c;
      --van-background: #000;
      --van-background-2: #1c1c1e;
      --van-background-3: #37363b
    }

    html {
      -webkit-tap-highlight-color: transparent
    }

    body {
      margin: 0;
      font-family: var(--van-base-font)
    }

    a {
      text-decoration: none
    }

    uni-input,
    uni-button,
    uni-textarea {
      color: inherit;
      font: inherit
    }

    a:focus,
    uni-input:focus,
    uni-button:focus,
    uni-textarea:focus,
    [class*=van-]:focus {
      outline: none
    }

    ol,
    ul {
      margin: 0;
      padding: 0;
      list-style: none
    }

    @keyframes van-slide-up-enter {
      0% {
        transform: translate3d(0, 100%, 0)
      }
    }

    @keyframes van-slide-up-leave {
      to {
        transform: translate3d(0, 100%, 0)
      }
    }

    @keyframes van-slide-down-enter {
      0% {
        transform: translate3d(0, -100%, 0)
      }
    }

    @keyframes van-slide-down-leave {
      to {
        transform: translate3d(0, -100%, 0)
      }
    }

    @keyframes van-slide-left-enter {
      0% {
        transform: translate3d(-100%, 0, 0)
      }
    }

    @keyframes van-slide-left-leave {
      to {
        transform: translate3d(-100%, 0, 0)
      }
    }

    @keyframes van-slide-right-enter {
      0% {
        transform: translate3d(100%, 0, 0)
      }
    }

    @keyframes van-slide-right-leave {
      to {
        transform: translate3d(100%, 0, 0)
      }
    }

    @keyframes van-fade-in {
      0% {
        opacity: 0
      }

      to {
        opacity: 1
      }
    }

    @keyframes van-fade-out {
      0% {
        opacity: 1
      }

      to {
        opacity: 0
      }
    }

    @keyframes van-rotate {
      0% {
        transform: rotate(0)
      }

      to {
        transform: rotate(360deg)
      }
    }

    .van-fade-enter-active {
      animation: var(--van-duration-base) van-fade-in both var(--van-ease-out)
    }

    .van-fade-leave-active {
      animation: var(--van-duration-base) van-fade-out both var(--van-ease-in)
    }

    .van-slide-up-enter-active {
      animation: van-slide-up-enter var(--van-duration-base) both var(--van-ease-out)
    }

    .van-slide-up-leave-active {
      animation: van-slide-up-leave var(--van-duration-base) both var(--van-ease-in)
    }

    .van-slide-down-enter-active {
      animation: van-slide-down-enter var(--van-duration-base) both var(--van-ease-out)
    }

    .van-slide-down-leave-active {
      animation: van-slide-down-leave var(--van-duration-base) both var(--van-ease-in)
    }

    .van-slide-left-enter-active {
      animation: van-slide-left-enter var(--van-duration-base) both var(--van-ease-out)
    }

    .van-slide-left-leave-active {
      animation: van-slide-left-leave var(--van-duration-base) both var(--van-ease-in)
    }

    .van-slide-right-enter-active {
      animation: van-slide-right-enter var(--van-duration-base) both var(--van-ease-out)
    }

    .van-slide-right-leave-active {
      animation: van-slide-right-leave var(--van-duration-base) both var(--van-ease-in)
    }

    .van-clearfix:after {
      display: table;
      clear: both;
      content: ""
    }

    .van-ellipsis {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis
    }

    .van-multi-ellipsis--l2 {
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical
    }

    .van-multi-ellipsis--l3 {
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical
    }

    .van-safe-area-top {
      padding-top: constant(safe-area-inset-top);
      padding-top: env(safe-area-inset-top)
    }

    .van-safe-area-bottom {
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom)
    }

    .van-haptics-feedback {
      cursor: pointer
    }

    .van-haptics-feedback:active {
      opacity: var(--van-active-opacity)
    }

    [class*=van-hairline]:after {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      top: -50%;
      right: -50%;
      bottom: -50%;
      left: -50%;
      border: 0 solid var(--van-border-color);
      transform: scale(.5)
    }

    .van-hairline,
    .van-hairline--top,
    .van-hairline--left,
    .van-hairline--right,
    .van-hairline--bottom,
    .van-hairline--surround,
    .van-hairline--top-bottom {
      position: relative
    }

    .van-hairline--top:after {
      border-top-width: var(--van-border-width)
    }

    .van-hairline--left:after {
      border-left-width: var(--van-border-width)
    }

    .van-hairline--right:after {
      border-right-width: var(--van-border-width)
    }

    .van-hairline--bottom:after {
      border-bottom-width: var(--van-border-width)
    }

    .van-hairline--top-bottom:after,
    .van-hairline-unset--top-bottom:after {
      border-width: var(--van-border-width) 0
    }

    .van-hairline--surround:after {
      border-width: var(--van-border-width)
    }

    :root {
      --van-action-bar-background: var(--van-background-2);
      --van-action-bar-height: 50px
    }

    .van-action-bar {
      position: fixed;
      right: 0;
      bottom: 0;
      left: 0;
      display: flex;
      align-items: center;
      box-sizing: content-box;
      height: var(--van-action-bar-height);
      background: var(--van-action-bar-background)
    }

    :root {
      --van-badge-size: 16px;
      --van-badge-color: var(--van-white);
      --van-badge-padding: 0 3px;
      --van-badge-font-size: var(--van-font-size-sm);
      --van-badge-font-weight: var(--van-font-bold);
      --van-badge-border-width: var(--van-border-width);
      --van-badge-background: var(--van-danger-color);
      --van-badge-dot-color: var(--van-danger-color);
      --van-badge-dot-size: 8px;
      --van-badge-font: -apple-system-font, helvetica neue, arial, sans-serif
    }

    .van-badge {
      display: inline-block;
      box-sizing: border-box;
      min-width: var(--van-badge-size);
      padding: var(--van-badge-padding);
      color: var(--van-badge-color);
      font-weight: var(--van-badge-font-weight);
      font-size: var(--van-badge-font-size);
      font-family: var(--van-badge-font);
      line-height: 1.2;
      text-align: center;
      background: var(--van-badge-background);
      border: var(--van-badge-border-width) solid var(--van-background-2);
      border-radius: var(--van-radius-max)
    }

    .van-badge--fixed {
      position: absolute;
      transform-origin: 100%
    }

    .van-badge--top-left {
      top: 0;
      left: 0;
      transform: translate(-50%, -50%)
    }

    .van-badge--top-right {
      top: 0;
      right: 0;
      transform: translate(50%, -50%)
    }

    .van-badge--bottom-left {
      bottom: 0;
      left: 0;
      transform: translate(-50%, 50%)
    }

    .van-badge--bottom-right {
      bottom: 0;
      right: 0;
      transform: translate(50%, 50%)
    }

    .van-badge--dot {
      width: var(--van-badge-dot-size);
      min-width: 0;
      height: var(--van-badge-dot-size);
      background: var(--van-badge-dot-color);
      border-radius: 100%;
      border: none;
      padding: 0
    }

    .van-badge__wrapper {
      position: relative;
      display: inline-block
    }

    .van-icon {
      position: relative;
      display: inline-block;
      font: 14px/1 vant-icon;
      font-size: inherit;
      text-rendering: auto;
      -webkit-font-smoothing: antialiased
    }

    .van-icon:before {
      display: inline-block
    }

    .van-icon-exchange:before {
      content: ""
    }

    .van-icon-eye:before {
      content: ""
    }

    .van-icon-enlarge:before {
      content: ""
    }

    .van-icon-expand-o:before {
      content: ""
    }

    .van-icon-eye-o:before {
      content: ""
    }

    .van-icon-expand:before {
      content: ""
    }

    .van-icon-filter-o:before {
      content: ""
    }

    .van-icon-fire:before {
      content: ""
    }

    .van-icon-fail:before {
      content: ""
    }

    .van-icon-failure:before {
      content: ""
    }

    .van-icon-fire-o:before {
      content: ""
    }

    .van-icon-flag-o:before {
      content: ""
    }

    .van-icon-font:before {
      content: ""
    }

    .van-icon-font-o:before {
      content: ""
    }

    .van-icon-gem-o:before {
      content: ""
    }

    .van-icon-flower-o:before {
      content: ""
    }

    .van-icon-gem:before {
      content: ""
    }

    .van-icon-gift-card:before {
      content: ""
    }

    .van-icon-friends:before {
      content: ""
    }

    .van-icon-friends-o:before {
      content: ""
    }

    .van-icon-gold-coin:before {
      content: ""
    }

    .van-icon-gold-coin-o:before {
      content: ""
    }

    .van-icon-good-job-o:before {
      content: ""
    }

    .van-icon-gift:before {
      content: ""
    }

    .van-icon-gift-o:before {
      content: ""
    }

    .van-icon-gift-card-o:before {
      content: ""
    }

    .van-icon-good-job:before {
      content: ""
    }

    .van-icon-home-o:before {
      content: ""
    }

    .van-icon-goods-collect:before {
      content: ""
    }

    .van-icon-graphic:before {
      content: ""
    }

    .van-icon-goods-collect-o:before {
      content: ""
    }

    .van-icon-hot-o:before {
      content: ""
    }

    .van-icon-info:before {
      content: ""
    }

    .van-icon-hotel-o:before {
      content: ""
    }

    .van-icon-info-o:before {
      content: ""
    }

    .van-icon-hot-sale-o:before {
      content: ""
    }

    .van-icon-hot:before {
      content: ""
    }

    .van-icon-like:before {
      content: ""
    }

    .van-icon-idcard:before {
      content: ""
    }

    .van-icon-invitation:before {
      content: ""
    }

    .van-icon-like-o:before {
      content: ""
    }

    .van-icon-hot-sale:before {
      content: ""
    }

    .van-icon-location-o:before {
      content: ""
    }

    .van-icon-location:before {
      content: ""
    }

    .van-icon-label:before {
      content: ""
    }

    .van-icon-lock:before {
      content: ""
    }

    .van-icon-label-o:before {
      content: ""
    }

    .van-icon-map-marked:before {
      content: ""
    }

    .van-icon-logistics:before {
      content: ""
    }

    .van-icon-manager:before {
      content: ""
    }

    .van-icon-more:before {
      content: ""
    }

    .van-icon-live:before {
      content: ""
    }

    .van-icon-manager-o:before {
      content: ""
    }

    .van-icon-medal:before {
      content: ""
    }

    .van-icon-more-o:before {
      content: ""
    }

    .van-icon-music-o:before {
      content: ""
    }

    .van-icon-music:before {
      content: ""
    }

    .van-icon-new-arrival-o:before {
      content: ""
    }

    .van-icon-medal-o:before {
      content: ""
    }

    .van-icon-new-o:before {
      content: ""
    }

    .van-icon-free-postage:before {
      content: ""
    }

    .van-icon-newspaper-o:before {
      content: ""
    }

    .van-icon-new-arrival:before {
      content: ""
    }

    .van-icon-minus:before {
      content: ""
    }

    .van-icon-orders-o:before {
      content: ""
    }

    .van-icon-new:before {
      content: ""
    }

    .van-icon-paid:before {
      content: ""
    }

    .van-icon-notes-o:before {
      content: ""
    }

    .van-icon-other-pay:before {
      content: ""
    }

    .van-icon-pause-circle:before {
      content: ""
    }

    .van-icon-pause:before {
      content: ""
    }

    .van-icon-pause-circle-o:before {
      content: ""
    }

    .van-icon-peer-pay:before {
      content: ""
    }

    .van-icon-pending-payment:before {
      content: ""
    }

    .van-icon-passed:before {
      content: ""
    }

    .van-icon-plus:before {
      content: ""
    }

    .van-icon-phone-circle-o:before {
      content: ""
    }

    .van-icon-phone-o:before {
      content: ""
    }

    .van-icon-printer:before {
      content: ""
    }

    .van-icon-photo-fail:before {
      content: ""
    }

    .van-icon-phone:before {
      content: ""
    }

    .van-icon-photo-o:before {
      content: ""
    }

    .van-icon-play-circle:before {
      content: ""
    }

    .van-icon-play:before {
      content: ""
    }

    .van-icon-phone-circle:before {
      content: ""
    }

    .van-icon-point-gift-o:before {
      content: ""
    }

    .van-icon-point-gift:before {
      content: ""
    }

    .van-icon-play-circle-o:before {
      content: ""
    }

    .van-icon-shrink:before {
      content: ""
    }

    .van-icon-photo:before {
      content: ""
    }

    .van-icon-qr:before {
      content: ""
    }

    .van-icon-qr-invalid:before {
      content: ""
    }

    .van-icon-question-o:before {
      content: ""
    }

    .van-icon-revoke:before {
      content: ""
    }

    .van-icon-replay:before {
      content: ""
    }

    .van-icon-service:before {
      content: ""
    }

    .van-icon-question:before {
      content: ""
    }

    .van-icon-search:before {
      content: ""
    }

    .van-icon-refund-o:before {
      content: ""
    }

    .van-icon-service-o:before {
      content: ""
    }

    .van-icon-scan:before {
      content: ""
    }

    .van-icon-share:before {
      content: ""
    }

    .van-icon-send-gift-o:before {
      content: ""
    }

    .van-icon-share-o:before {
      content: ""
    }

    .van-icon-setting:before {
      content: ""
    }

    .van-icon-points:before {
      content: ""
    }

    .van-icon-photograph:before {
      content: ""
    }

    .van-icon-shop:before {
      content: ""
    }

    .van-icon-shop-o:before {
      content: ""
    }

    .van-icon-shop-collect-o:before {
      content: ""
    }

    .van-icon-shop-collect:before {
      content: ""
    }

    .van-icon-smile:before {
      content: ""
    }

    .van-icon-shopping-cart-o:before {
      content: ""
    }

    .van-icon-sign:before {
      content: ""
    }

    .van-icon-sort:before {
      content: ""
    }

    .van-icon-star-o:before {
      content: ""
    }

    .van-icon-smile-comment-o:before {
      content: ""
    }

    .van-icon-stop:before {
      content: ""
    }

    .van-icon-stop-circle-o:before {
      content: ""
    }

    .van-icon-smile-o:before {
      content: ""
    }

    .van-icon-star:before {
      content: ""
    }

    .van-icon-success:before {
      content: ""
    }

    .van-icon-stop-circle:before {
      content: ""
    }

    .van-icon-records:before {
      content: ""
    }

    .van-icon-shopping-cart:before {
      content: ""
    }

    .van-icon-tosend:before {
      content: ""
    }

    .van-icon-todo-list:before {
      content: ""
    }

    .van-icon-thumb-circle-o:before {
      content: ""
    }

    .van-icon-thumb-circle:before {
      content: ""
    }

    .van-icon-umbrella-circle:before {
      content: ""
    }

    .van-icon-underway:before {
      content: ""
    }

    .van-icon-upgrade:before {
      content: ""
    }

    .van-icon-todo-list-o:before {
      content: ""
    }

    .van-icon-tv-o:before {
      content: ""
    }

    .van-icon-underway-o:before {
      content: ""
    }

    .van-icon-user-o:before {
      content: ""
    }

    .van-icon-vip-card-o:before {
      content: ""
    }

    .van-icon-vip-card:before {
      content: ""
    }

    .van-icon-send-gift:before {
      content: ""
    }

    .van-icon-wap-home:before {
      content: ""
    }

    .van-icon-wap-nav:before {
      content: ""
    }

    .van-icon-volume-o:before {
      content: ""
    }

    .van-icon-video:before {
      content: ""
    }

    .van-icon-wap-home-o:before {
      content: ""
    }

    .van-icon-volume:before {
      content: ""
    }

    .van-icon-warning:before {
      content: ""
    }

    .van-icon-weapp-nav:before {
      content: ""
    }

    .van-icon-wechat-pay:before {
      content: ""
    }

    .van-icon-warning-o:before {
      content: ""
    }

    .van-icon-wechat:before {
      content: ""
    }

    .van-icon-setting-o:before {
      content: ""
    }

    .van-icon-youzan-shield:before {
      content: ""
    }

    .van-icon-warn-o:before {
      content: ""
    }

    .van-icon-smile-comment:before {
      content: ""
    }

    .van-icon-user-circle-o:before {
      content: ""
    }

    .van-icon-video-o:before {
      content: ""
    }

    .van-icon-add-square:before {
      content: ""
    }

    .van-icon-add:before {
      content: ""
    }

    .van-icon-arrow-down:before {
      content: ""
    }

    .van-icon-arrow-up:before {
      content: ""
    }

    .van-icon-arrow:before {
      content: ""
    }

    .van-icon-after-sale:before {
      content: ""
    }

    .van-icon-add-o:before {
      content: ""
    }

    .van-icon-alipay:before {
      content: ""
    }

    .van-icon-ascending:before {
      content: ""
    }

    .van-icon-apps-o:before {
      content: ""
    }

    .van-icon-aim:before {
      content: ""
    }

    .van-icon-award:before {
      content: ""
    }

    .van-icon-arrow-left:before {
      content: ""
    }

    .van-icon-award-o:before {
      content: ""
    }

    .van-icon-audio:before {
      content: ""
    }

    .van-icon-bag-o:before {
      content: ""
    }

    .van-icon-balance-list:before {
      content: ""
    }

    .van-icon-back-top:before {
      content: ""
    }

    .van-icon-bag:before {
      content: ""
    }

    .van-icon-balance-pay:before {
      content: ""
    }

    .van-icon-balance-o:before {
      content: ""
    }

    .van-icon-bar-chart-o:before {
      content: ""
    }

    .van-icon-bars:before {
      content: ""
    }

    .van-icon-balance-list-o:before {
      content: ""
    }

    .van-icon-birthday-cake-o:before {
      content: ""
    }

    .van-icon-bookmark:before {
      content: ""
    }

    .van-icon-bill:before {
      content: ""
    }

    .van-icon-bell:before {
      content: ""
    }

    .van-icon-browsing-history-o:before {
      content: ""
    }

    .van-icon-browsing-history:before {
      content: ""
    }

    .van-icon-bookmark-o:before {
      content: ""
    }

    .van-icon-bulb-o:before {
      content: ""
    }

    .van-icon-bullhorn-o:before {
      content: ""
    }

    .van-icon-bill-o:before {
      content: ""
    }

    .van-icon-calendar-o:before {
      content: ""
    }

    .van-icon-brush-o:before {
      content: ""
    }

    .van-icon-card:before {
      content: ""
    }

    .van-icon-cart-o:before {
      content: ""
    }

    .van-icon-cart-circle:before {
      content: ""
    }

    .van-icon-cart-circle-o:before {
      content: ""
    }

    .van-icon-cart:before {
      content: ""
    }

    .van-icon-cash-on-deliver:before {
      content: ""
    }

    .van-icon-cash-back-record:before {
      content: ""
    }

    .van-icon-cashier-o:before {
      content: ""
    }

    .van-icon-chart-trending-o:before {
      content: ""
    }

    .van-icon-certificate:before {
      content: ""
    }

    .van-icon-chat:before {
      content: ""
    }

    .van-icon-clear:before {
      content: ""
    }

    .van-icon-chat-o:before {
      content: ""
    }

    .van-icon-checked:before {
      content: ""
    }

    .van-icon-clock:before {
      content: ""
    }

    .van-icon-clock-o:before {
      content: ""
    }

    .van-icon-close:before {
      content: ""
    }

    .van-icon-closed-eye:before {
      content: ""
    }

    .van-icon-circle:before {
      content: ""
    }

    .van-icon-cluster-o:before {
      content: ""
    }

    .van-icon-column:before {
      content: ""
    }

    .van-icon-comment-circle-o:before {
      content: ""
    }

    .van-icon-cluster:before {
      content: ""
    }

    .van-icon-comment:before {
      content: ""
    }

    .van-icon-comment-o:before {
      content: ""
    }

    .van-icon-comment-circle:before {
      content: ""
    }

    .van-icon-completed:before {
      content: ""
    }

    .van-icon-credit-pay:before {
      content: ""
    }

    .van-icon-coupon:before {
      content: ""
    }

    .van-icon-debit-pay:before {
      content: ""
    }

    .van-icon-coupon-o:before {
      content: ""
    }

    .van-icon-contact:before {
      content: ""
    }

    .van-icon-descending:before {
      content: ""
    }

    .van-icon-desktop-o:before {
      content: ""
    }

    .van-icon-diamond-o:before {
      content: ""
    }

    .van-icon-description:before {
      content: ""
    }

    .van-icon-delete:before {
      content: ""
    }

    .van-icon-diamond:before {
      content: ""
    }

    .van-icon-delete-o:before {
      content: ""
    }

    .van-icon-cross:before {
      content: ""
    }

    .van-icon-edit:before {
      content: ""
    }

    .van-icon-ellipsis:before {
      content: ""
    }

    .van-icon-down:before {
      content: ""
    }

    .van-icon-discount:before {
      content: ""
    }

    .van-icon-ecard-pay:before {
      content: ""
    }

    .van-icon-envelop-o:before {
      content: ""
    }

    .van-icon-shield-o:before {
      content: ""
    }

    .van-icon-guide-o:before {
      content: ""
    }

    .van-icon-cash-o:before {
      content: ""
    }

    .van-icon-qq:before {
      content: ""
    }

    .van-icon-wechat-moments:before {
      content: ""
    }

    .van-icon-weibo:before {
      content: ""
    }

    .van-icon-link-o:before {
      content: ""
    }

    .van-icon-miniprogram-o:before {
      content: ""
    }

    @font-face {
      font-weight: 400;
      font-family: vant-icon;
      font-style: normal;
      /*savepage-font-display=auto*/
        /*savepage-url=//at.alicdn.com/t/c/font_2553510_ovbl29ce9ud.woff?t=1672541115585*/
        url() format("woff")
    }

    .van-icon__image {
      display: block;
      width: 1em;
      height: 1em;
      object-fit: contain
    }

    :root {
      --van-skeleton-image-size: 96px;
      --van-skeleton-image-radius: 24px
    }

    .van-skeleton-image {
      display: flex;
      width: var(--van-skeleton-image-size);
      height: var(--van-skeleton-image-size);
      align-items: center;
      justify-content: center;
      background: var(--van-active-color)
    }

    .van-skeleton-image--round {
      border-radius: var(--van-skeleton-image-radius)
    }

    .van-skeleton-image__icon {
      width: calc(var(--van-skeleton-image-size) / 2);
      height: calc(var(--van-skeleton-image-size) / 2);
      font-size: calc(var(--van-skeleton-image-size) / 2);
      color: var(--van-gray-5)
    }

    :root {
      --van-rate-icon-size: 20px;
      --van-rate-icon-gutter: var(--van-padding-base);
      --van-rate-icon-void-color: var(--van-gray-5);
      --van-rate-icon-full-color: var(--van-danger-color);
      --van-rate-icon-disabled-color: var(--van-gray-5)
    }

    .van-rate {
      display: inline-flex;
      cursor: pointer;
      -webkit-user-select: none;
      user-select: none;
      flex-wrap: wrap
    }

    .van-rate__item {
      position: relative
    }

    .van-rate__item:not(:last-child) {
      padding-right: var(--van-rate-icon-gutter)
    }

    .van-rate__icon {
      display: block;
      width: 1em;
      color: var(--van-rate-icon-void-color);
      font-size: var(--van-rate-icon-size)
    }

    .van-rate__icon--half {
      position: absolute;
      top: 0;
      left: 0;
      overflow: hidden
    }

    .van-rate__icon--full {
      color: var(--van-rate-icon-full-color)
    }

    .van-rate__icon--disabled {
      color: var(--van-rate-icon-disabled-color)
    }

    .van-rate--disabled {
      cursor: not-allowed
    }

    .van-rate--readonly {
      cursor: default
    }

    :root {
      --van-notice-bar-height: 40px;
      --van-notice-bar-padding: 0 var(--van-padding-md);
      --van-notice-bar-wrapable-padding: var(--van-padding-xs) var(--van-padding-md);
      --van-notice-bar-text-color: var(--van-orange-dark);
      --van-notice-bar-font-size: var(--van-font-size-md);
      --van-notice-bar-line-height: 24px;
      --van-notice-bar-background: var(--van-orange-light);
      --van-notice-bar-icon-size: 16px;
      --van-notice-bar-icon-min-width: 24px
    }

    .van-notice-bar {
      position: relative;
      display: flex;
      align-items: center;
      height: var(--van-notice-bar-height);
      padding: var(--van-notice-bar-padding);
      color: var(--van-notice-bar-text-color);
      font-size: var(--van-notice-bar-font-size);
      line-height: var(--van-notice-bar-line-height);
      background: var(--van-notice-bar-background)
    }

    .van-notice-bar__left-icon,
    .van-notice-bar__right-icon {
      min-width: var(--van-notice-bar-icon-min-width);
      font-size: var(--van-notice-bar-icon-size)
    }

    .van-notice-bar__right-icon {
      text-align: right;
      cursor: pointer
    }

    .van-notice-bar__wrap {
      position: relative;
      display: flex;
      flex: 1;
      align-items: center;
      height: 100%;
      overflow: hidden
    }

    .van-notice-bar__content {
      position: absolute;
      white-space: nowrap;
      transition-timing-function: linear
    }

    .van-notice-bar__content.van-ellipsis {
      max-width: 100%
    }

    .van-notice-bar--wrapable {
      height: auto;
      padding: var(--van-notice-bar-wrapable-padding)
    }

    .van-notice-bar--wrapable .van-notice-bar__wrap {
      height: auto
    }

    .van-notice-bar--wrapable .van-notice-bar__content {
      position: relative;
      white-space: normal;
      word-wrap: break-word
    }

    :root {
      --van-nav-bar-height: 46px;
      --van-nav-bar-background: var(--van-background-2);
      --van-nav-bar-arrow-size: 16px;
      --van-nav-bar-icon-color: var(--van-primary-color);
      --van-nav-bar-text-color: var(--van-primary-color);
      --van-nav-bar-title-font-size: var(--van-font-size-lg);
      --van-nav-bar-title-text-color: var(--van-text-color);
      --van-nav-bar-z-index: 1
    }

    .van-nav-bar {
      position: relative;
      z-index: var(--van-nav-bar-z-index);
      line-height: var(--van-line-height-lg);
      text-align: center;
      background: var(--van-nav-bar-background);
      -webkit-user-select: none;
      user-select: none
    }

    .van-nav-bar--fixed {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%
    }

    .van-nav-bar--safe-area-inset-top {
      padding-top: constant(safe-area-inset-top);
      padding-top: env(safe-area-inset-top)
    }

    .van-nav-bar .van-icon {
      color: var(--van-nav-bar-icon-color)
    }

    .van-nav-bar__content {
      position: relative;
      display: flex;
      align-items: center;
      height: var(--van-nav-bar-height)
    }

    .van-nav-bar__arrow {
      margin-right: var(--van-padding-base);
      font-size: var(--van-nav-bar-arrow-size)
    }

    .van-nav-bar__title {
      max-width: 60%;
      margin: 0 auto;
      color: var(--van-nav-bar-title-text-color);
      font-weight: var(--van-font-bold);
      font-size: var(--van-nav-bar-title-font-size)
    }

    .van-nav-bar__left,
    .van-nav-bar__right {
      position: absolute;
      top: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      padding: 0 var(--van-padding-md);
      font-size: var(--van-font-size-md)
    }

    .van-nav-bar__left {
      left: 0
    }

    .van-nav-bar__right {
      right: 0
    }

    .van-nav-bar__text {
      color: var(--van-nav-bar-text-color)
    }

    :root {
      --van-image-placeholder-text-color: var(--van-text-color-2);
      --van-image-placeholder-font-size: var(--van-font-size-md);
      --van-image-placeholder-background: var(--van-background);
      --van-image-loading-icon-size: 32px;
      --van-image-loading-icon-color: var(--van-gray-4);
      --van-image-error-icon-size: 32px;
      --van-image-error-icon-color: var(--van-gray-4)
    }

    .van-image {
      position: relative;
      display: inline-block
    }

    .van-image--round {
      overflow: hidden;
      border-radius: var(--van-radius-max)
    }

    .van-image--round .van-image__img {
      border-radius: inherit
    }

    .van-image--block {
      display: block
    }

    .van-image__img,
    .van-image__error,
    .van-image__loading {
      display: block;
      width: 100%;
      height: 100%
    }

    .van-image__error,
    .van-image__loading {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--van-image-placeholder-text-color);
      font-size: var(--van-image-placeholder-font-size);
      background: var(--van-image-placeholder-background)
    }

    .van-image__loading-icon {
      color: var(--van-image-loading-icon-color);
      font-size: var(--van-image-loading-icon-size)
    }

    .van-image__error-icon {
      color: var(--van-image-error-icon-color);
      font-size: var(--van-image-error-icon-size)
    }

    :root {
      --van-back-top-size: 40px;
      --van-back-top-right: 30px;
      --van-back-top-bottom: 40px;
      --van-back-top-z-index: 100;
      --van-back-top-icon-size: 20px;
      --van-back-top-text-color: #fff;
      --van-back-top-background: var(--van-blue)
    }

    .van-back-top {
      position: fixed;
      display: flex;
      align-items: center;
      justify-content: center;
      width: var(--van-back-top-size);
      height: var(--van-back-top-size);
      right: var(--van-back-top-right);
      bottom: var(--van-back-top-bottom);
      z-index: var(--van-back-top-z-index);
      cursor: pointer;
      color: var(--van-back-top-text-color);
      border-radius: var(--van-radius-max);
      box-shadow: 0 2px 8px rgba(0, 0, 0, .12);
      transform: scale(0);
      transition: var(--van-duration-base) cubic-bezier(.25, .8, .5, 1);
      background-color: var(--van-back-top-background)
    }

    .van-back-top:active {
      opacity: var(--van-active-opacity)
    }

    .van-back-top__placeholder {
      display: none
    }

    .van-back-top--active {
      transform: scale(1)
    }

    .van-back-top__icon {
      font-size: var(--van-back-top-icon-size);
      font-weight: var(--van-font-bold)
    }

    :root {
      --van-tag-padding: 0 var(--van-padding-base);
      --van-tag-text-color: var(--van-white);
      --van-tag-font-size: var(--van-font-size-sm);
      --van-tag-radius: 2px;
      --van-tag-line-height: 16px;
      --van-tag-medium-padding: 2px 6px;
      --van-tag-large-padding: var(--van-padding-base) var(--van-padding-xs);
      --van-tag-large-radius: var(--van-radius-md);
      --van-tag-large-font-size: var(--van-font-size-md);
      --van-tag-round-radius: var(--van-radius-max);
      --van-tag-danger-color: var(--van-danger-color);
      --van-tag-primary-color: var(--van-primary-color);
      --van-tag-success-color: var(--van-success-color);
      --van-tag-warning-color: var(--van-warning-color);
      --van-tag-default-color: var(--van-gray-6);
      --van-tag-plain-background: var(--van-background-2)
    }

    .van-tag {
      position: relative;
      display: inline-flex;
      align-items: center;
      padding: var(--van-tag-padding);
      color: var(--van-tag-text-color);
      font-size: var(--van-tag-font-size);
      line-height: var(--van-tag-line-height);
      border-radius: var(--van-tag-radius)
    }

    .van-tag--default {
      background: var(--van-tag-default-color)
    }

    .van-tag--default.van-tag--plain {
      color: var(--van-tag-default-color)
    }

    .van-tag--danger {
      background: var(--van-tag-danger-color)
    }

    .van-tag--danger.van-tag--plain {
      color: var(--van-tag-danger-color)
    }

    .van-tag--primary {
      background: var(--van-tag-primary-color)
    }

    .van-tag--primary.van-tag--plain {
      color: var(--van-tag-primary-color)
    }

    .van-tag--success {
      background: var(--van-tag-success-color)
    }

    .van-tag--success.van-tag--plain {
      color: var(--van-tag-success-color)
    }

    .van-tag--warning {
      background: var(--van-tag-warning-color)
    }

    .van-tag--warning.van-tag--plain {
      color: var(--van-tag-warning-color)
    }

    .van-tag--plain {
      background: var(--van-tag-plain-background);
      border-color: currentColor
    }

    .van-tag--plain:before {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      border: 1px solid;
      border-color: inherit;
      border-radius: inherit;
      content: "";
      pointer-events: none
    }

    .van-tag--medium {
      padding: var(--van-tag-medium-padding)
    }

    .van-tag--large {
      padding: var(--van-tag-large-padding);
      font-size: var(--van-tag-large-font-size);
      border-radius: var(--van-tag-large-radius)
    }

    .van-tag--mark {
      border-radius: 0 var(--van-tag-round-radius) var(--van-tag-round-radius) 0
    }

    .van-tag--mark:after {
      display: block;
      width: 2px;
      content: ""
    }

    .van-tag--round {
      border-radius: var(--van-tag-round-radius)
    }

    .van-tag__close {
      margin-left: 2px
    }

    :root {
      --van-card-padding: var(--van-padding-xs) var(--van-padding-md);
      --van-card-font-size: var(--van-font-size-sm);
      --van-card-text-color: var(--van-text-color);
      --van-card-background: var(--van-background);
      --van-card-thumb-size: 88px;
      --van-card-thumb-radius: var(--van-radius-lg);
      --van-card-title-line-height: 16px;
      --van-card-desc-color: var(--van-text-color-2);
      --van-card-desc-line-height: var(--van-line-height-md);
      --van-card-price-color: var(--van-text-color);
      --van-card-origin-price-color: var(--van-text-color-2);
      --van-card-num-color: var(--van-text-color-2);
      --van-card-origin-price-font-size: var(--van-font-size-xs);
      --van-card-price-font-size: var(--van-font-size-sm);
      --van-card-price-integer-font-size: var(--van-font-size-lg);
      --van-card-price-font: var(--van-price-font)
    }

    .van-card {
      position: relative;
      box-sizing: border-box;
      padding: var(--van-card-padding);
      color: var(--van-card-text-color);
      font-size: var(--van-card-font-size);
      background: var(--van-card-background)
    }

    .van-card:not(:first-child) {
      margin-top: var(--van-padding-xs)
    }

    .van-card__header {
      display: flex
    }

    .van-card__thumb {
      position: relative;
      flex: none;
      width: var(--van-card-thumb-size);
      height: var(--van-card-thumb-size);
      margin-right: var(--van-padding-xs)
    }

    .van-card__thumb img {
      border-radius: var(--van-card-thumb-radius)
    }

    .van-card__content {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: space-between;
      min-width: 0;
      min-height: var(--van-card-thumb-size)
    }

    .van-card__content--centered {
      justify-content: center
    }

    .van-card__title,
    .van-card__desc {
      word-wrap: break-word
    }

    .van-card__title {
      max-height: 32px;
      font-weight: var(--van-font-bold);
      line-height: var(--van-card-title-line-height)
    }

    .van-card__desc {
      max-height: var(--van-card-desc-line-height);
      color: var(--van-card-desc-color);
      line-height: var(--van-card-desc-line-height)
    }

    .van-card__bottom {
      line-height: var(--van-line-height-md)
    }

    .van-card__price {
      display: inline-block;
      color: var(--van-card-price-color);
      font-weight: var(--van-font-bold);
      font-size: var(--van-card-price-font-size)
    }

    .van-card__price-integer {
      font-size: var(--van-card-price-integer-font-size);
      font-family: var(--van-card-price-font)
    }

    .van-card__price-decimal {
      font-family: var(--van-card-price-font)
    }

    .van-card__origin-price {
      display: inline-block;
      margin-left: 5px;
      color: var(--van-card-origin-price-color);
      font-size: var(--van-card-origin-price-font-size);
      text-decoration: line-through
    }

    .van-card__num {
      float: right;
      color: var(--van-card-num-color)
    }

    .van-card__tag {
      position: absolute;
      top: 2px;
      left: 0
    }

    .van-card__footer {
      flex: none;
      text-align: right
    }

    .van-card__footer .van-button {
      margin-left: 5px
    }

    :root {
      --van-cell-font-size: var(--van-font-size-md);
      --van-cell-line-height: 24px;
      --van-cell-vertical-padding: 10px;
      --van-cell-horizontal-padding: var(--van-padding-md);
      --van-cell-text-color: var(--van-text-color);
      --van-cell-background: var(--van-background-2);
      --van-cell-border-color: var(--van-border-color);
      --van-cell-active-color: var(--van-active-color);
      --van-cell-required-color: var(--van-danger-color);
      --van-cell-label-color: var(--van-text-color-2);
      --van-cell-label-font-size: var(--van-font-size-sm);
      --van-cell-label-line-height: var(--van-line-height-sm);
      --van-cell-label-margin-top: var(--van-padding-base);
      --van-cell-value-color: var(--van-text-color-2);
      --van-cell-icon-size: 16px;
      --van-cell-right-icon-color: var(--van-gray-6);
      --van-cell-large-vertical-padding: var(--van-padding-sm);
      --van-cell-large-title-font-size: var(--van-font-size-lg);
      --van-cell-large-label-font-size: var(--van-font-size-md)
    }

    .van-cell {
      position: relative;
      display: flex;
      box-sizing: border-box;
      width: 100%;
      padding: var(--van-cell-vertical-padding) var(--van-cell-horizontal-padding);
      overflow: hidden;
      color: var(--van-cell-text-color);
      font-size: var(--van-cell-font-size);
      line-height: var(--van-cell-line-height);
      background: var(--van-cell-background)
    }

    .van-cell:after {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      right: var(--van-padding-md);
      bottom: 0;
      left: var(--van-padding-md);
      border-bottom: 1px solid var(--van-cell-border-color);
      transform: scaleY(.5)
    }

    .van-cell:last-child:after,
    .van-cell--borderless:after {
      display: none
    }

    .van-cell__label {
      margin-top: var(--van-cell-label-margin-top);
      color: var(--van-cell-label-color);
      font-size: var(--van-cell-label-font-size);
      line-height: var(--van-cell-label-line-height)
    }

    .van-cell__title,
    .van-cell__value {
      flex: 1
    }

    .van-cell__value {
      position: relative;
      overflow: hidden;
      color: var(--van-cell-value-color);
      text-align: right;
      vertical-align: middle;
      word-wrap: break-word
    }

    .van-cell__left-icon,
    .van-cell__right-icon {
      height: var(--van-cell-line-height);
      font-size: var(--van-cell-icon-size);
      line-height: var(--van-cell-line-height)
    }

    .van-cell__left-icon {
      margin-right: var(--van-padding-base)
    }

    .van-cell__right-icon {
      margin-left: var(--van-padding-base);
      color: var(--van-cell-right-icon-color)
    }

    .van-cell--clickable {
      cursor: pointer
    }

    .van-cell--clickable:active {
      background-color: var(--van-cell-active-color)
    }

    .van-cell--required {
      overflow: visible
    }

    .van-cell--required:before {
      position: absolute;
      left: var(--van-padding-xs);
      color: var(--van-cell-required-color);
      font-size: var(--van-cell-font-size);
      content: "*"
    }

    .van-cell--center {
      align-items: center
    }

    .van-cell--large {
      padding-top: var(--van-cell-large-vertical-padding);
      padding-bottom: var(--van-cell-large-vertical-padding)
    }

    .van-cell--large .van-cell__title {
      font-size: var(--van-cell-large-title-font-size)
    }

    .van-cell--large .van-cell__label {
      font-size: var(--van-cell-large-label-font-size)
    }

    :root {
      --van-coupon-cell-selected-text-color: var(--van-text-color)
    }

    .van-coupon-cell__value--selected {
      color: var(--van-coupon-cell-selected-text-color)
    }

    :root {
      --van-contact-card-padding: var(--van-padding-md);
      --van-contact-card-add-icon-size: 40px;
      --van-contact-card-add-icon-color: var(--van-primary-color);
      --van-contact-card-title-line-height: var(--van-line-height-md)
    }

    .van-contact-card {
      padding: var(--van-contact-card-padding)
    }

    .van-contact-card__title {
      margin-left: 5px;
      line-height: var(--van-contact-card-title-line-height)
    }

    .van-contact-card--add .van-contact-card__value {
      line-height: var(--van-contact-card-add-icon-size)
    }

    .van-contact-card--add .van-cell__left-icon {
      color: var(--van-contact-card-add-icon-color);
      font-size: var(--van-contact-card-add-icon-size)
    }

    .van-contact-card:before {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 2px;
      background: repeating-linear-gradient(-45deg, var(--van-warning-color) 0, var(--van-warning-color) 20%, transparent 0, transparent 25%, var(--van-primary-color) 0, var(--van-primary-color) 45%, transparent 0, transparent 50%);
      background-size: 80px;
      content: ""
    }

    :root {
      --van-collapse-item-duration: var(--van-duration-base);
      --van-collapse-item-content-padding: var(--van-padding-sm) var(--van-padding-md);
      --van-collapse-item-content-font-size: var(--van-font-size-md);
      --van-collapse-item-content-line-height: 1.5;
      --van-collapse-item-content-text-color: var(--van-text-color-2);
      --van-collapse-item-content-background: var(--van-background-2);
      --van-collapse-item-title-disabled-color: var(--van-text-color-3)
    }

    .van-collapse-item {
      position: relative
    }

    .van-collapse-item--border:after {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      top: 0;
      right: var(--van-padding-md);
      left: var(--van-padding-md);
      border-top: 1px solid var(--van-border-color);
      transform: scaleY(.5)
    }

    .van-collapse-item__title .van-cell__right-icon:before {
      transform: rotate(90deg) translateZ(0);
      transition: transform var(--van-collapse-item-duration)
    }

    .van-collapse-item__title:after {
      right: var(--van-padding-md);
      display: none
    }

    .van-collapse-item__title--expanded .van-cell__right-icon:before {
      transform: rotate(-90deg)
    }

    .van-collapse-item__title--expanded:after {
      display: block
    }

    .van-collapse-item__title--borderless:after {
      display: none
    }

    .van-collapse-item__title--disabled {
      cursor: not-allowed
    }

    .van-collapse-item__title--disabled,
    .van-collapse-item__title--disabled .van-cell__right-icon {
      color: var(--van-collapse-item-title-disabled-color)
    }

    .van-collapse-item__wrapper {
      overflow: hidden;
      transition: height var(--van-collapse-item-duration) ease-in-out;
      will-change: height
    }

    .van-collapse-item__content {
      padding: var(--van-collapse-item-content-padding);
      color: var(--van-collapse-item-content-text-color);
      font-size: var(--van-collapse-item-content-font-size);
      line-height: var(--van-collapse-item-content-line-height);
      background: var(--van-collapse-item-content-background)
    }

    :root {
      --van-field-label-width: 6.2em;
      --van-field-label-color: var(--van-text-color);
      --van-field-label-margin-right: var(--van-padding-sm);
      --van-field-input-text-color: var(--van-text-color);
      --van-field-input-error-text-color: var(--van-danger-color);
      --van-field-input-disabled-text-color: var(--van-text-color-3);
      --van-field-placeholder-text-color: var(--van-text-color-3);
      --van-field-icon-size: 18px;
      --van-field-clear-icon-size: 18px;
      --van-field-clear-icon-color: var(--van-gray-5);
      --van-field-right-icon-color: var(--van-gray-6);
      --van-field-error-message-color: var(--van-danger-color);
      --van-field-error-message-font-size: 12px;
      --van-field-text-area-min-height: 60px;
      --van-field-word-limit-color: var(--van-gray-7);
      --van-field-word-limit-font-size: var(--van-font-size-sm);
      --van-field-word-limit-line-height: 16px;
      --van-field-disabled-text-color: var(--van-text-color-3);
      --van-field-required-mark-color: var(--van-red)
    }

    .van-field {
      flex-wrap: wrap
    }

    .van-field__label {
      flex: none;
      box-sizing: border-box;
      width: var(--van-field-label-width);
      margin-right: var(--van-field-label-margin-right);
      color: var(--van-field-label-color);
      text-align: left;
      word-wrap: break-word
    }

    .van-field__label--center {
      text-align: center
    }

    .van-field__label--right {
      text-align: right
    }

    .van-field__label--top {
      display: flex;
      width: 100%;
      text-align: left;
      margin-bottom: var(--van-padding-base);
      word-break: break-word
    }

    .van-field__label--required:before {
      margin-right: 2px;
      color: var(--van-field-required-mark-color);
      content: "*"
    }

    .van-field--disabled .van-field__label {
      color: var(--van-field-disabled-text-color)
    }

    .van-field__value {
      overflow: visible
    }

    .van-field__body {
      display: flex;
      align-items: center
    }

    .van-field__control {
      display: block;
      box-sizing: border-box;
      width: 100%;
      min-width: 0;
      margin: 0;
      padding: 0;
      color: var(--van-field-input-text-color);
      line-height: inherit;
      text-align: left;
      background-color: transparent;
      border: 0;
      resize: none;
      -webkit-user-select: auto;
      user-select: auto
    }

    .van-field__control::-webkit-input-placeholder {
      color: var(--van-field-placeholder-text-color)
    }

    .van-field__control::placeholder {
      color: var(--van-field-placeholder-text-color)
    }

    .van-field__control:read-only {
      cursor: default
    }

    .van-field__control:disabled {
      color: var(--van-field-input-disabled-text-color);
      cursor: not-allowed;
      opacity: 1;
      -webkit-text-fill-color: var(--van-field-input-disabled-text-color)
    }

    .van-field__control--center {
      justify-content: center;
      text-align: center
    }

    .van-field__control--right {
      justify-content: flex-end;
      text-align: right
    }

    .van-field__control--custom {
      display: flex;
      align-items: center;
      min-height: var(--van-cell-line-height)
    }

    .van-field__control--error::-webkit-input-placeholder {
      color: var(--van-field-input-error-text-color);
      -webkit-text-fill-color: currentColor
    }

    .van-field__control--error,
    .van-field__control--error::placeholder {
      color: var(--van-field-input-error-text-color);
      -webkit-text-fill-color: currentColor
    }

    .van-field__control--min-height {
      min-height: var(--van-field-text-area-min-height)
    }

    .van-field__control[type=date],
    .van-field__control[type=time],
    .van-field__control[type=datetime-local] {
      min-height: var(--van-cell-line-height)
    }

    .van-field__control[type=search] {
      -webkit-appearance: none
    }

    .van-field__clear,
    .van-field__icon,
    .van-field__button,
    .van-field__right-icon {
      flex-shrink: 0
    }

    .van-field__clear,
    .van-field__right-icon {
      margin-right: calc(var(--van-padding-xs) * -1);
      padding: 0 var(--van-padding-xs);
      line-height: inherit
    }

    .van-field__clear {
      color: var(--van-field-clear-icon-color);
      font-size: var(--van-field-clear-icon-size);
      cursor: pointer
    }

    .van-field__left-icon .van-icon,
    .van-field__right-icon .van-icon {
      display: block;
      font-size: var(--van-field-icon-size);
      line-height: inherit
    }

    .van-field__left-icon {
      margin-right: var(--van-padding-base)
    }

    .van-field__right-icon {
      color: var(--van-field-right-icon-color)
    }

    .van-field__button {
      padding-left: var(--van-padding-xs)
    }

    .van-field__error-message {
      color: var(--van-field-error-message-color);
      font-size: var(--van-field-error-message-font-size);
      text-align: left
    }

    .van-field__error-message--center {
      text-align: center
    }

    .van-field__error-message--right {
      text-align: right
    }

    .van-field__word-limit {
      margin-top: var(--van-padding-base);
      color: var(--van-field-word-limit-color);
      font-size: var(--van-field-word-limit-font-size);
      line-height: var(--van-field-word-limit-line-height);
      text-align: right
    }

    :root {
      --van-search-padding: 10px var(--van-padding-sm);
      --van-search-background: var(--van-background-2);
      --van-search-content-background: var(--van-background);
      --van-search-input-height: 34px;
      --van-search-label-padding: 0 5px;
      --van-search-label-color: var(--van-text-color);
      --van-search-label-font-size: var(--van-font-size-md);
      --van-search-left-icon-color: var(--van-gray-6);
      --van-search-action-padding: 0 var(--van-padding-xs);
      --van-search-action-text-color: var(--van-text-color);
      --van-search-action-font-size: var(--van-font-size-md)
    }

    .van-search {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: var(--van-search-padding);
      background: var(--van-search-background)
    }

    .van-search__content {
      display: flex;
      flex: 1;
      padding-left: var(--van-padding-sm);
      background: var(--van-search-content-background);
      border-radius: var(--van-radius-sm)
    }

    .van-search__content--round {
      border-radius: var(--van-radius-max)
    }

    .van-search__label {
      padding: var(--van-search-label-padding);
      color: var(--van-search-label-color);
      font-size: var(--van-search-label-font-size);
      line-height: var(--van-search-input-height)
    }

    .van-search__field {
      flex: 1;
      align-items: center;
      padding: 0 var(--van-padding-xs) 0 0;
      height: var(--van-search-input-height);
      background-color: transparent
    }

    .van-search__field .van-field__left-icon {
      color: var(--van-search-left-icon-color)
    }

    .van-search--show-action {
      padding-right: 0
    }

    .van-search uni-input::-webkit-search-decoration,
    .van-search uni-input::-webkit-search-cancel-button,
    .van-search uni-input::-webkit-search-results-button,
    .van-search uni-input::-webkit-search-results-decoration {
      display: none
    }

    .van-search__action {
      padding: var(--van-search-action-padding);
      color: var(--van-search-action-text-color);
      font-size: var(--van-search-action-font-size);
      line-height: var(--van-search-input-height);
      cursor: pointer;
      -webkit-user-select: none;
      user-select: none
    }

    .van-search__action:active {
      background-color: var(--van-active-color)
    }

    :root {
      --van-action-bar-icon-width: 48px;
      --van-action-bar-icon-height: 100%;
      --van-action-bar-icon-color: var(--van-text-color);
      --van-action-bar-icon-size: 18px;
      --van-action-bar-icon-font-size: var(--van-font-size-xs);
      --van-action-bar-icon-active-color: var(--van-active-color);
      --van-action-bar-icon-text-color: var(--van-text-color);
      --van-action-bar-icon-background: var(--van-background-2)
    }

    .van-action-bar-icon {
      display: flex;
      flex-direction: column;
      justify-content: center;
      min-width: var(--van-action-bar-icon-width);
      height: var(--van-action-bar-icon-height);
      color: var(--van-action-bar-icon-text-color);
      font-size: var(--van-action-bar-icon-font-size);
      line-height: 1;
      text-align: center;
      background: var(--van-action-bar-icon-background);
      cursor: pointer
    }

    .van-action-bar-icon:active {
      background-color: var(--van-action-bar-icon-active-color)
    }

    .van-action-bar-icon__icon {
      margin: 0 auto var(--van-padding-base);
      color: var(--van-action-bar-icon-color);
      font-size: var(--van-action-bar-icon-size)
    }

    :root {
      --van-loading-text-color: var(--van-text-color-2);
      --van-loading-text-font-size: var(--van-font-size-md);
      --van-loading-spinner-color: var(--van-gray-5);
      --van-loading-spinner-size: 30px;
      --van-loading-spinner-duration: .8s
    }

    .van-loading {
      position: relative;
      color: var(--van-loading-spinner-color);
      font-size: 0;
      vertical-align: middle
    }

    .van-loading__spinner {
      position: relative;
      display: inline-block;
      width: var(--van-loading-spinner-size);
      max-width: 100%;
      height: var(--van-loading-spinner-size);
      max-height: 100%;
      vertical-align: middle;
      animation: van-rotate var(--van-loading-spinner-duration) linear infinite
    }

    .van-loading__spinner--spinner {
      animation-timing-function: steps(12)
    }

    .van-loading__spinner--circular {
      animation-duration: 2s
    }

    .van-loading__line {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%
    }

    .van-loading__line:before {
      display: block;
      width: 2px;
      height: 25%;
      margin: 0 auto;
      background-color: currentColor;
      border-radius: 40%;
      content: " "
    }

    .van-loading__circular {
      display: block;
      width: 100%;
      height: 100%
    }

    .van-loading__circular circle {
      animation: van-circular 1.5s ease-in-out infinite;
      stroke: currentColor;
      stroke-width: 3;
      stroke-linecap: round
    }

    .van-loading__text {
      display: inline-block;
      margin-left: var(--van-padding-xs);
      color: var(--van-loading-text-color);
      font-size: var(--van-loading-text-font-size);
      vertical-align: middle
    }

    .van-loading--vertical {
      display: flex;
      flex-direction: column;
      align-items: center
    }

    .van-loading--vertical .van-loading__text {
      margin: var(--van-padding-xs) 0 0
    }

    @keyframes van-circular {
      0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0
      }

      50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -40
      }

      to {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -120
      }
    }

    .van-loading__line--1 {
      transform: rotate(30deg);
      opacity: 1
    }

    .van-loading__line--2 {
      transform: rotate(60deg);
      opacity: .9375
    }

    .van-loading__line--3 {
      transform: rotate(90deg);
      opacity: .875
    }

    .van-loading__line--4 {
      transform: rotate(120deg);
      opacity: .8125
    }

    .van-loading__line--5 {
      transform: rotate(150deg);
      opacity: .75
    }

    .van-loading__line--6 {
      transform: rotate(180deg);
      opacity: .6875
    }

    .van-loading__line--7 {
      transform: rotate(210deg);
      opacity: .625
    }

    .van-loading__line--8 {
      transform: rotate(240deg);
      opacity: .5625
    }

    .van-loading__line--9 {
      transform: rotate(270deg);
      opacity: .5
    }

    .van-loading__line--10 {
      transform: rotate(300deg);
      opacity: .4375
    }

    .van-loading__line--11 {
      transform: rotate(330deg);
      opacity: .375
    }

    .van-loading__line--12 {
      transform: rotate(360deg);
      opacity: .3125
    }

    :root {
      --van-pull-refresh-head-height: 50px;
      --van-pull-refresh-head-font-size: var(--van-font-size-md);
      --van-pull-refresh-head-text-color: var(--van-text-color-2);
      --van-pull-refresh-loading-icon-size: 16px
    }

    .van-pull-refresh {
      overflow: hidden
    }

    .van-pull-refresh__track {
      position: relative;
      height: 100%;
      transition-property: transform
    }

    .van-pull-refresh__head {
      position: absolute;
      left: 0;
      width: 100%;
      height: var(--van-pull-refresh-head-height);
      overflow: hidden;
      color: var(--van-pull-refresh-head-text-color);
      font-size: var(--van-pull-refresh-head-font-size);
      line-height: var(--van-pull-refresh-head-height);
      text-align: center;
      transform: translateY(-100%)
    }

    .van-pull-refresh__loading .van-loading__spinner {
      width: var(--van-pull-refresh-loading-icon-size);
      height: var(--van-pull-refresh-loading-icon-size)
    }

    :root {
      --van-number-keyboard-background: var(--van-gray-2);
      --van-number-keyboard-key-height: 48px;
      --van-number-keyboard-key-font-size: 28px;
      --van-number-keyboard-key-active-color: var(--van-gray-3);
      --van-number-keyboard-key-background: var(--van-background-2);
      --van-number-keyboard-delete-font-size: var(--van-font-size-lg);
      --van-number-keyboard-title-color: var(--van-gray-7);
      --van-number-keyboard-title-height: 34px;
      --van-number-keyboard-title-font-size: var(--van-font-size-lg);
      --van-number-keyboard-close-padding: 0 var(--van-padding-md);
      --van-number-keyboard-close-color: var(--van-primary-color);
      --van-number-keyboard-close-font-size: var(--van-font-size-md);
      --van-number-keyboard-button-text-color: var(--van-white);
      --van-number-keyboard-button-background: var(--van-primary-color);
      --van-number-keyboard-z-index: 100
    }

    .van-theme-dark {
      --van-number-keyboard-background: var(--van-gray-8);
      --van-number-keyboard-key-background: var(--van-gray-7);
      --van-number-keyboard-key-active-color: var(--van-gray-6)
    }

    .van-number-keyboard {
      position: fixed;
      bottom: 0;
      left: 0;
      z-index: var(--van-number-keyboard-z-index);
      width: 100%;
      padding-bottom: 22px;
      background: var(--van-number-keyboard-background);
      -webkit-user-select: none;
      user-select: none
    }

    .van-number-keyboard--with-title {
      border-radius: 20px 20px 0 0
    }

    .van-number-keyboard__header {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: content-box;
      height: var(--van-number-keyboard-title-height);
      padding-top: 6px;
      color: var(--van-number-keyboard-title-color);
      font-size: var(--van-number-keyboard-title-font-size)
    }

    .van-number-keyboard__title {
      display: inline-block;
      font-weight: 400
    }

    .van-number-keyboard__title-left {
      position: absolute;
      left: 0
    }

    .van-number-keyboard__body {
      display: flex;
      padding: 6px 0 0 6px
    }

    .van-number-keyboard__keys {
      display: flex;
      flex: 3;
      flex-wrap: wrap
    }

    .van-number-keyboard__close {
      position: absolute;
      right: 0;
      height: 100%;
      padding: var(--van-number-keyboard-close-padding);
      color: var(--van-number-keyboard-close-color);
      font-size: var(--van-number-keyboard-close-font-size);
      background-color: transparent;
      border: none
    }

    .van-number-keyboard__sidebar {
      display: flex;
      flex: 1;
      flex-direction: column
    }

    .van-number-keyboard--unfit {
      padding-bottom: 0
    }

    .van-key {
      display: flex;
      align-items: center;
      justify-content: center;
      height: var(--van-number-keyboard-key-height);
      font-size: var(--van-number-keyboard-key-font-size);
      line-height: 1.5;
      background: var(--van-number-keyboard-key-background);
      border-radius: var(--van-radius-lg);
      cursor: pointer
    }

    .van-key--large {
      position: absolute;
      top: 0;
      right: 6px;
      bottom: 6px;
      left: 0;
      height: auto
    }

    .van-key--blue,
    .van-key--delete {
      font-size: var(--van-number-keyboard-delete-font-size)
    }

    .van-key--active {
      background-color: var(--van-number-keyboard-key-active-color)
    }

    .van-key--blue {
      color: var(--van-number-keyboard-button-text-color);
      background: var(--van-number-keyboard-button-background)
    }

    .van-key--blue.van-key--active {
      opacity: var(--van-active-opacity)
    }

    .van-key__wrapper {
      position: relative;
      flex: 1;
      flex-basis: 33%;
      box-sizing: border-box;
      padding: 0 6px 6px 0
    }

    .van-key__wrapper--wider {
      flex-basis: 66%
    }

    .van-key__delete-icon {
      width: 32px;
      height: 22px
    }

    .van-key__collapse-icon {
      width: 30px;
      height: 24px
    }

    .van-key__loading-icon {
      color: var(--van-number-keyboard-button-text-color)
    }

    :root {
      --van-list-text-color: var(--van-text-color-2);
      --van-list-text-font-size: var(--van-font-size-md);
      --van-list-text-line-height: 50px;
      --van-list-loading-icon-size: 16px
    }

    .van-list__loading,
    .van-list__finished-text,
    .van-list__error-text {
      color: var(--van-list-text-color);
      font-size: var(--van-list-text-font-size);
      line-height: var(--van-list-text-line-height);
      text-align: center
    }

    .van-list__placeholder {
      height: 0;
      pointer-events: none
    }

    .van-list__loading-icon .van-loading__spinner {
      width: var(--van-list-loading-icon-size);
      height: var(--van-list-loading-icon-size)
    }

    :root {
      --van-switch-size: 26px;
      --van-switch-width: calc(1.8em + 4px);
      --van-switch-height: calc(1em + 4px);
      --van-switch-node-size: 1em;
      --van-switch-node-background: var(--van-white);
      --van-switch-node-shadow: 0 3px 1px 0 rgba(0, 0, 0, .05);
      --van-switch-background: rgba(120, 120, 128, .16);
      --van-switch-on-background: var(--van-primary-color);
      --van-switch-duration: var(--van-duration-base);
      --van-switch-disabled-opacity: var(--van-disabled-opacity)
    }

    .van-theme-dark {
      --van-switch-background: rgba(120, 120, 128, .32)
    }

    .van-switch {
      position: relative;
      display: inline-block;
      box-sizing: content-box;
      width: var(--van-switch-width);
      height: var(--van-switch-height);
      font-size: var(--van-switch-size);
      background: var(--van-switch-background);
      border-radius: var(--van-switch-node-size);
      cursor: pointer;
      transition: background-color var(--van-switch-duration)
    }

    .van-switch__node {
      position: absolute;
      top: 2px;
      left: 2px;
      width: var(--van-switch-node-size);
      height: var(--van-switch-node-size);
      font-size: inherit;
      background: var(--van-switch-node-background);
      border-radius: 100%;
      box-shadow: var(--van-switch-node-shadow);
      transition: transform var(--van-switch-duration) cubic-bezier(.3, 1.05, .4, 1.05)
    }

    .van-switch__loading {
      top: 25%;
      left: 25%;
      width: 50%;
      height: 50%;
      line-height: 1
    }

    .van-switch--on {
      background: var(--van-switch-on-background)
    }

    .van-switch--on .van-switch__node {
      transform: translate(calc(var(--van-switch-width) - var(--van-switch-node-size) - 4px))
    }

    .van-switch--on .van-switch__loading {
      color: var(--van-switch-on-background)
    }

    .van-switch--disabled {
      cursor: not-allowed;
      opacity: var(--van-switch-disabled-opacity)
    }

    .van-switch--loading {
      cursor: default
    }

    :root {
      --van-button-mini-height: 24px;
      --van-button-mini-padding: 0 var(--van-padding-base);
      --van-button-mini-font-size: var(--van-font-size-xs);
      --van-button-small-height: 32px;
      --van-button-small-padding: 0 var(--van-padding-xs);
      --van-button-small-font-size: var(--van-font-size-sm);
      --van-button-normal-padding: 0 15px;
      --van-button-normal-font-size: var(--van-font-size-md);
      --van-button-large-height: 50px;
      --van-button-default-height: 44px;
      --van-button-default-line-height: 1.2;
      --van-button-default-font-size: var(--van-font-size-lg);
      --van-button-default-color: var(--van-text-color);
      --van-button-default-background: var(--van-background-2);
      --van-button-default-border-color: var(--van-gray-4);
      --van-button-primary-color: var(--van-white);
      --van-button-primary-background: var(--van-primary-color);
      --van-button-primary-border-color: var(--van-primary-color);
      --van-button-success-color: var(--van-white);
      --van-button-success-background: var(--van-success-color);
      --van-button-success-border-color: var(--van-success-color);
      --van-button-danger-color: var(--van-white);
      --van-button-danger-background: var(--van-danger-color);
      --van-button-danger-border-color: var(--van-danger-color);
      --van-button-warning-color: var(--van-white);
      --van-button-warning-background: var(--van-warning-color);
      --van-button-warning-border-color: var(--van-warning-color);
      --van-button-border-width: var(--van-border-width);
      --van-button-radius: var(--van-radius-md);
      --van-button-round-radius: var(--van-radius-max);
      --van-button-plain-background: var(--van-white);
      --van-button-disabled-opacity: var(--van-disabled-opacity);
      --van-button-icon-size: 1.2em;
      --van-button-loading-icon-size: 20px
    }

    .van-theme-dark {
      --van-button-plain-background: transparent
    }

    .van-button {
      position: relative;
      display: inline-block;
      box-sizing: border-box;
      height: var(--van-button-default-height);
      margin: 0;
      padding: 0;
      font-size: var(--van-button-default-font-size);
      line-height: var(--van-button-default-line-height);
      text-align: center;
      border-radius: var(--van-button-radius);
      cursor: pointer;
      transition: opacity var(--van-duration-fast);
      -webkit-appearance: none;
      -webkit-font-smoothing: auto
    }

    .van-button:before {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100%;
      height: 100%;
      background: var(--van-black);
      border: inherit;
      border-color: var(--van-black);
      border-radius: inherit;
      transform: translate(-50%, -50%);
      opacity: 0;
      content: " "
    }

    .van-button:active:before {
      opacity: .1
    }

    .van-button--loading:before,
    .van-button--disabled:before {
      display: none
    }

    .van-button--default {
      color: var(--van-button-default-color);
      background: var(--van-button-default-background);
      border: var(--van-button-border-width) solid var(--van-button-default-border-color)
    }

    .van-button--primary {
      color: var(--van-button-primary-color);
      background: var(--van-button-primary-background);
      border: var(--van-button-border-width) solid var(--van-button-primary-border-color)
    }

    .van-button--success {
      color: var(--van-button-success-color);
      background: var(--van-button-success-background);
      border: var(--van-button-border-width) solid var(--van-button-success-border-color)
    }

    .van-button--danger {
      color: var(--van-button-danger-color);
      background: var(--van-button-danger-background);
      border: var(--van-button-border-width) solid var(--van-button-danger-border-color)
    }

    .van-button--warning {
      color: var(--van-button-warning-color);
      background: var(--van-button-warning-background);
      border: var(--van-button-border-width) solid var(--van-button-warning-border-color)
    }

    .van-button--plain {
      background: var(--van-button-plain-background)
    }

    .van-button--plain.van-button--primary {
      color: var(--van-button-primary-background)
    }

    .van-button--plain.van-button--success {
      color: var(--van-button-success-background)
    }

    .van-button--plain.van-button--danger {
      color: var(--van-button-danger-background)
    }

    .van-button--plain.van-button--warning {
      color: var(--van-button-warning-background)
    }

    .van-button--large {
      width: 100%;
      height: var(--van-button-large-height)
    }

    .van-button--normal {
      padding: var(--van-button-normal-padding);
      font-size: var(--van-button-normal-font-size)
    }

    .van-button--small {
      height: var(--van-button-small-height);
      padding: var(--van-button-small-padding);
      font-size: var(--van-button-small-font-size)
    }

    .van-button__loading {
      color: inherit;
      font-size: inherit
    }

    .van-button__loading .van-loading__spinner {
      color: currentColor;
      width: var(--van-button-loading-icon-size);
      height: var(--van-button-loading-icon-size)
    }

    .van-button--mini {
      height: var(--van-button-mini-height);
      padding: var(--van-button-mini-padding);
      font-size: var(--van-button-mini-font-size)
    }

    .van-button--mini+.van-button--mini {
      margin-left: var(--van-padding-base)
    }

    .van-button--block {
      display: block;
      width: 100%
    }

    .van-button--disabled {
      cursor: not-allowed;
      opacity: var(--van-button-disabled-opacity)
    }

    .van-button--loading {
      cursor: default
    }

    .van-button--round {
      border-radius: var(--van-button-round-radius)
    }

    .van-button--square {
      border-radius: 0
    }

    .van-button__content {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%
    }

    .van-button__content:before {
      content: " "
    }

    .van-button__icon {
      font-size: var(--van-button-icon-size);
      line-height: inherit
    }

    .van-button__icon+.van-button__text,
    .van-button__loading+.van-button__text,
    .van-button__text+.van-button__icon,
    .van-button__text+.van-button__loading {
      margin-left: var(--van-padding-base)
    }

    .van-button--hairline {
      border-width: 0
    }

    .van-button--hairline:after {
      border-color: inherit;
      border-radius: calc(var(--van-button-radius) * 2)
    }

    .van-button--hairline.van-button--round:after {
      border-radius: var(--van-button-round-radius)
    }

    .van-button--hairline.van-button--square:after {
      border-radius: 0
    }

    :root {
      --van-submit-bar-height: 50px;
      --van-submit-bar-z-index: 100;
      --van-submit-bar-background: var(--van-background-2);
      --van-submit-bar-button-width: 110px;
      --van-submit-bar-price-color: var(--van-danger-color);
      --van-submit-bar-price-font-size: var(--van-font-size-sm);
      --van-submit-bar-price-integer-font-size: 20px;
      --van-submit-bar-price-font: var(--van-price-font);
      --van-submit-bar-text-color: var(--van-text-color);
      --van-submit-bar-text-font-size: var(--van-font-size-md);
      --van-submit-bar-tip-padding: var(--van-padding-xs) var(--van-padding-sm);
      --van-submit-bar-tip-font-size: var(--van-font-size-sm);
      --van-submit-bar-tip-line-height: 1.5;
      --van-submit-bar-tip-color: var(--van-orange-dark);
      --van-submit-bar-tip-background: var(--van-orange-light);
      --van-submit-bar-tip-icon-size: 12px;
      --van-submit-bar-button-height: 40px;
      --van-submit-bar-padding: 0 var(--van-padding-md)
    }

    .van-submit-bar {
      position: fixed;
      bottom: 0;
      left: 0;
      z-index: var(--van-submit-bar-z-index);
      width: 100%;
      background: var(--van-submit-bar-background);
      -webkit-user-select: none;
      user-select: none
    }

    .van-submit-bar__tip {
      padding: var(--van-submit-bar-tip-padding);
      color: var(--van-submit-bar-tip-color);
      font-size: var(--van-submit-bar-tip-font-size);
      line-height: var(--van-submit-bar-tip-line-height);
      background: var(--van-submit-bar-tip-background)
    }

    .van-submit-bar__tip-icon {
      margin-right: var(--van-padding-base);
      font-size: var(--van-submit-bar-tip-icon-size);
      vertical-align: middle
    }

    .van-submit-bar__tip-text {
      vertical-align: middle
    }

    .van-submit-bar__bar {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: var(--van-submit-bar-height);
      padding: var(--van-submit-bar-padding);
      font-size: var(--van-submit-bar-text-font-size)
    }

    .van-submit-bar__text {
      flex: 1;
      padding-right: var(--van-padding-sm);
      color: var(--van-submit-bar-text-color);
      text-align: right
    }

    .van-submit-bar__text span {
      display: inline-block
    }

    .van-submit-bar__suffix-label {
      margin-left: var(--van-padding-base);
      font-weight: var(--van-font-bold)
    }

    .van-submit-bar__price {
      color: var(--van-submit-bar-price-color);
      font-weight: var(--van-font-bold);
      font-size: var(--van-submit-bar-price-font-size);
      margin-left: var(--van-padding-base)
    }

    .van-submit-bar__price-integer {
      font-size: var(--van-submit-bar-price-integer-font-size);
      font-family: var(--van-submit-bar-price-font)
    }

    .van-submit-bar__button {
      width: var(--van-submit-bar-button-width);
      height: var(--van-submit-bar-button-height);
      font-weight: var(--van-font-bold);
      border: none
    }

    .van-submit-bar__button--danger {
      background: var(--van-gradient-red)
    }

    :root {
      --van-signature-padding: var(--van-padding-xs);
      --van-signature-content-height: 200px;
      --van-signature-content-background: var(--van-background-2);
      --van-signature-content-border: 1px dotted #dadada
    }

    .van-signature {
      padding: var(--van-signature-padding)
    }

    .van-signature__content {
      display: flex;
      justify-content: center;
      align-items: center;
      height: var(--van-signature-content-height);
      background-color: var(--van-signature-content-background);
      border: var(--van-signature-content-border);
      border-radius: var(--van-radius-lg);
      overflow: hidden
    }

    .van-signature__content uni-canvas {
      width: 100%;
      height: 100%
    }

    .van-signature__footer {
      display: flex;
      justify-content: flex-end
    }

    .van-signature__footer .van-button {
      padding: 0 var(--van-padding-md);
      margin-top: var(--van-padding-xs);
      margin-left: var(--van-padding-xs)
    }

    :root {
      --van-contact-edit-padding: var(--van-padding-md);
      --van-contact-edit-fields-radius: var(--van-radius-md);
      --van-contact-edit-buttons-padding: var(--van-padding-xl) 0;
      --van-contact-edit-button-margin-bottom: var(--van-padding-sm);
      --van-contact-edit-button-font-size: var(--van-font-size-lg);
      --van-contact-edit-field-label-width: 4.1em
    }

    .van-contact-edit {
      padding: var(--van-contact-edit-padding)
    }

    .van-contact-edit__fields {
      overflow: hidden;
      border-radius: var(--van-contact-edit-fields-radius)
    }

    .van-contact-edit__fields .van-field__label {
      width: var(--van-contact-edit-field-label-width)
    }

    .van-contact-edit__switch-cell {
      margin-top: 10px;
      padding-top: 9px;
      padding-bottom: 9px;
      border-radius: var(--van-contact-edit-fields-radius)
    }

    .van-contact-edit__buttons {
      padding: var(--van-contact-edit-buttons-padding)
    }

    .van-contact-edit__button {
      margin-bottom: var(--van-contact-edit-button-margin-bottom);
      font-size: var(--van-contact-edit-button-font-size)
    }

    :root {
      --van-action-bar-button-height: 40px;
      --van-action-bar-button-warning-color: var(--van-gradient-orange);
      --van-action-bar-button-danger-color: var(--van-gradient-red)
    }

    .van-action-bar-button {
      flex: 1;
      height: var(--van-action-bar-button-height);
      font-weight: var(--van-font-bold);
      font-size: var(--van-font-size-md);
      border: none;
      border-radius: 0
    }

    .van-action-bar-button--first {
      margin-left: 5px;
      border-top-left-radius: var(--van-radius-max);
      border-bottom-left-radius: var(--van-radius-max)
    }

    .van-action-bar-button--last {
      margin-right: 5px;
      border-top-right-radius: var(--van-radius-max);
      border-bottom-right-radius: var(--van-radius-max)
    }

    .van-action-bar-button--warning {
      background: var(--van-action-bar-button-warning-color)
    }

    .van-action-bar-button--danger {
      background: var(--van-action-bar-button-danger-color)
    }

    @media (max-width: 321px) {
      .van-action-bar-button {
        font-size: 13px
      }
    }

    :root {
      --van-overlay-z-index: 1;
      --van-overlay-background: rgba(0, 0, 0, .7)
    }

    .van-overlay {
      position: fixed;
      top: 0;
      left: 0;
      z-index: var(--van-overlay-z-index);
      width: 100%;
      height: 100%;
      background: var(--van-overlay-background)
    }

    :root {
      --van-popup-background: var(--van-background-2);
      --van-popup-transition: transform var(--van-duration-base);
      --van-popup-round-radius: 16px;
      --van-popup-close-icon-size: 22px;
      --van-popup-close-icon-color: var(--van-gray-5);
      --van-popup-close-icon-margin: 16px;
      --van-popup-close-icon-z-index: 1
    }

    .van-overflow-hidden {
      overflow: hidden !important
    }

    .van-popup {
      position: fixed;
      max-height: 100%;
      overflow-y: auto;
      box-sizing: border-box;
      background: var(--van-popup-background);
      transition: var(--van-popup-transition);
      -webkit-overflow-scrolling: touch
    }

    .van-popup--center {
      top: 50%;
      left: 0;
      right: 0;
      width: -webkit-fit-content;
      width: fit-content;
      max-width: calc(100vw - var(--van-padding-md) * 2);
      margin: 0 auto;
      transform: translateY(-50%)
    }

    .van-popup--center.van-popup--round {
      border-radius: var(--van-popup-round-radius)
    }

    .van-popup--top {
      top: 0;
      left: 0;
      width: 100%
    }

    .van-popup--top.van-popup--round {
      border-radius: 0 0 var(--van-popup-round-radius) var(--van-popup-round-radius)
    }

    .van-popup--right {
      top: 50%;
      right: 0;
      transform: translate3d(0, -50%, 0)
    }

    .van-popup--right.van-popup--round {
      border-radius: var(--van-popup-round-radius) 0 0 var(--van-popup-round-radius)
    }

    .van-popup--bottom {
      bottom: 0;
      left: 0;
      width: 100%
    }

    .van-popup--bottom.van-popup--round {
      border-radius: var(--van-popup-round-radius) var(--van-popup-round-radius) 0 0
    }

    .van-popup--left {
      top: 50%;
      left: 0;
      transform: translate3d(0, -50%, 0)
    }

    .van-popup--left.van-popup--round {
      border-radius: 0 var(--van-popup-round-radius) var(--van-popup-round-radius) 0
    }

    .van-popup-slide-top-enter-active,
    .van-popup-slide-left-enter-active,
    .van-popup-slide-right-enter-active,
    .van-popup-slide-bottom-enter-active {
      transition-timing-function: var(--van-ease-out)
    }

    .van-popup-slide-top-leave-active,
    .van-popup-slide-left-leave-active,
    .van-popup-slide-right-leave-active,
    .van-popup-slide-bottom-leave-active {
      transition-timing-function: var(--van-ease-in)
    }

    .van-popup-slide-top-enter-from,
    .van-popup-slide-top-leave-active {
      transform: translate3d(0, -100%, 0)
    }

    .van-popup-slide-right-enter-from,
    .van-popup-slide-right-leave-active {
      transform: translate3d(100%, -50%, 0)
    }

    .van-popup-slide-bottom-enter-from,
    .van-popup-slide-bottom-leave-active {
      transform: translate3d(0, 100%, 0)
    }

    .van-popup-slide-left-enter-from,
    .van-popup-slide-left-leave-active {
      transform: translate3d(-100%, -50%, 0)
    }

    .van-popup__close-icon {
      position: absolute;
      z-index: var(--van-popup-close-icon-z-index);
      color: var(--van-popup-close-icon-color);
      font-size: var(--van-popup-close-icon-size)
    }

    .van-popup__close-icon--top-left {
      top: var(--van-popup-close-icon-margin);
      left: var(--van-popup-close-icon-margin)
    }

    .van-popup__close-icon--top-right {
      top: var(--van-popup-close-icon-margin);
      right: var(--van-popup-close-icon-margin)
    }

    .van-popup__close-icon--bottom-left {
      bottom: var(--van-popup-close-icon-margin);
      left: var(--van-popup-close-icon-margin)
    }

    .van-popup__close-icon--bottom-right {
      right: var(--van-popup-close-icon-margin);
      bottom: var(--van-popup-close-icon-margin)
    }

    :root {
      --van-share-sheet-header-padding: var(--van-padding-sm) var(--van-padding-md);
      --van-share-sheet-title-color: var(--van-text-color);
      --van-share-sheet-title-font-size: var(--van-font-size-md);
      --van-share-sheet-title-line-height: var(--van-line-height-md);
      --van-share-sheet-description-color: var(--van-text-color-2);
      --van-share-sheet-description-font-size: var(--van-font-size-sm);
      --van-share-sheet-description-line-height: 16px;
      --van-share-sheet-icon-size: 48px;
      --van-share-sheet-option-name-color: var(--van-gray-7);
      --van-share-sheet-option-name-font-size: var(--van-font-size-sm);
      --van-share-sheet-option-description-color: var(--van-text-color-3);
      --van-share-sheet-option-description-font-size: var(--van-font-size-sm);
      --van-share-sheet-cancel-button-font-size: var(--van-font-size-lg);
      --van-share-sheet-cancel-button-height: 48px;
      --van-share-sheet-cancel-button-background: var(--van-background-2)
    }

    .van-share-sheet__header {
      padding: var(--van-share-sheet-header-padding);
      text-align: center
    }

    .van-share-sheet__title {
      margin-top: var(--van-padding-xs);
      color: var(--van-share-sheet-title-color);
      font-weight: 400;
      font-size: var(--van-share-sheet-title-font-size);
      line-height: var(--van-share-sheet-title-line-height)
    }

    .van-share-sheet__description {
      display: block;
      margin-top: var(--van-padding-xs);
      color: var(--van-share-sheet-description-color);
      font-size: var(--van-share-sheet-description-font-size);
      line-height: var(--van-share-sheet-description-line-height)
    }

    .van-share-sheet__options {
      position: relative;
      display: flex;
      padding: var(--van-padding-md) 0 var(--van-padding-md) var(--van-padding-xs);
      overflow-x: auto;
      overflow-y: visible;
      -webkit-overflow-scrolling: touch
    }

    .van-share-sheet__options--border:before {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      top: 0;
      right: 0;
      left: var(--van-padding-md);
      border-top: 1px solid var(--van-border-color);
      transform: scaleY(.5)
    }

    .van-share-sheet__options::-webkit-scrollbar {
      height: 0
    }

    .van-share-sheet__option {
      display: flex;
      flex-direction: column;
      align-items: center;
      -webkit-user-select: none;
      user-select: none
    }

    .van-share-sheet__icon,
    .van-share-sheet__image-icon {
      width: var(--van-share-sheet-icon-size);
      height: var(--van-share-sheet-icon-size);
      margin: 0 var(--van-padding-md)
    }

    .van-share-sheet__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--van-gray-7);
      border-radius: 100%;
      background-color: var(--van-gray-2)
    }

    .van-share-sheet__icon--link,
    .van-share-sheet__icon--poster,
    .van-share-sheet__icon--qrcode {
      font-size: 26px
    }

    .van-share-sheet__icon--weapp-qrcode {
      font-size: 28px
    }

    .van-share-sheet__icon--qq,
    .van-share-sheet__icon--weibo,
    .van-share-sheet__icon--wechat,
    .van-share-sheet__icon--wechat-moments {
      font-size: 30px;
      color: var(--van-white)
    }

    .van-share-sheet__icon--qq {
      background-color: #38b9fa
    }

    .van-share-sheet__icon--wechat {
      background-color: #0bc15f
    }

    .van-share-sheet__icon--weibo {
      background-color: #ee575e
    }

    .van-share-sheet__icon--wechat-moments {
      background-color: #7bc845
    }

    .van-share-sheet__name {
      margin-top: var(--van-padding-xs);
      padding: 0 var(--van-padding-base);
      color: var(--van-share-sheet-option-name-color);
      font-size: var(--van-share-sheet-option-name-font-size)
    }

    .van-share-sheet__option-description {
      padding: 0 var(--van-padding-base);
      color: var(--van-share-sheet-option-description-color);
      font-size: var(--van-share-sheet-option-description-font-size)
    }

    .van-share-sheet__cancel {
      display: block;
      width: 100%;
      padding: 0;
      font-size: var(--van-share-sheet-cancel-button-font-size);
      line-height: var(--van-share-sheet-cancel-button-height);
      text-align: center;
      background: var(--van-share-sheet-cancel-button-background);
      border: none;
      cursor: pointer
    }

    .van-share-sheet__cancel:before {
      display: block;
      height: var(--van-padding-xs);
      background-color: var(--van-background);
      content: " "
    }

    .van-share-sheet__cancel:active {
      background-color: var(--van-active-color)
    }

    :root {
      --van-popover-arrow-size: 6px;
      --van-popover-radius: var(--van-radius-lg);
      --van-popover-action-width: 128px;
      --van-popover-action-height: 44px;
      --van-popover-action-font-size: var(--van-font-size-md);
      --van-popover-action-line-height: var(--van-line-height-md);
      --van-popover-action-icon-size: 20px;
      --van-popover-horizontal-action-height: 34px;
      --van-popover-horizontal-action-icon-size: 16px;
      --van-popover-light-text-color: var(--van-text-color);
      --van-popover-light-background: var(--van-background-2);
      --van-popover-light-action-disabled-text-color: var(--van-text-color-3);
      --van-popover-dark-text-color: var(--van-white);
      --van-popover-dark-background: #4a4a4a;
      --van-popover-dark-action-disabled-text-color: var(--van-text-color-2)
    }

    .van-popover {
      position: absolute;
      overflow: visible;
      background-color: transparent;
      transition: opacity .15s, transform .15s
    }

    .van-popover__wrapper {
      display: inline-block
    }

    .van-popover__arrow {
      position: absolute;
      width: 0;
      height: 0;
      border-color: transparent;
      border-style: solid;
      border-width: var(--van-popover-arrow-size)
    }

    .van-popover__content {
      overflow: hidden;
      border-radius: var(--van-popover-radius)
    }

    .van-popover__content--horizontal {
      display: flex;
      width: -webkit-max-content;
      width: max-content
    }

    .van-popover__content--horizontal .van-popover__action {
      flex: none;
      width: auto;
      height: var(--van-popover-horizontal-action-height);
      padding: 0 var(--van-padding-sm)
    }

    .van-popover__content--horizontal .van-popover__action:last-child:after {
      display: none
    }

    .van-popover__content--horizontal .van-popover__action-icon {
      margin-right: var(--van-padding-base);
      font-size: var(--van-popover-horizontal-action-icon-size)
    }

    .van-popover__action {
      position: relative;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      width: var(--van-popover-action-width);
      height: var(--van-popover-action-height);
      padding: 0 var(--van-padding-md);
      font-size: var(--van-popover-action-font-size);
      line-height: var(--van-line-height-md);
      cursor: pointer
    }

    .van-popover__action:last-child .van-popover__action-text:after {
      display: none
    }

    .van-popover__action-text {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      height: 100%
    }

    .van-popover__action-icon {
      margin-right: var(--van-padding-xs);
      font-size: var(--van-popover-action-icon-size)
    }

    .van-popover__action--with-icon .van-popover__action-text {
      justify-content: flex-start
    }

    .van-popover[data-popper-placement^=top] .van-popover__arrow {
      bottom: 0;
      border-top-color: currentColor;
      border-bottom-width: 0;
      margin-bottom: calc(var(--van-popover-arrow-size) * -1)
    }

    .van-popover[data-popper-placement=top] {
      transform-origin: 50% 100%
    }

    .van-popover[data-popper-placement=top] .van-popover__arrow {
      left: 50%;
      transform: translate(-50%)
    }

    .van-popover[data-popper-placement=top-start] {
      transform-origin: 0 100%
    }

    .van-popover[data-popper-placement=top-start] .van-popover__arrow {
      left: var(--van-padding-md)
    }

    .van-popover[data-popper-placement=top-end] {
      transform-origin: 100% 100%
    }

    .van-popover[data-popper-placement=top-end] .van-popover__arrow {
      right: var(--van-padding-md)
    }

    .van-popover[data-popper-placement^=left] .van-popover__arrow {
      right: 0;
      border-right-width: 0;
      border-left-color: currentColor;
      margin-right: calc(var(--van-popover-arrow-size) * -1)
    }

    .van-popover[data-popper-placement=left] {
      transform-origin: 100% 50%
    }

    .van-popover[data-popper-placement=left] .van-popover__arrow {
      top: 50%;
      transform: translateY(-50%)
    }

    .van-popover[data-popper-placement=left-start] {
      transform-origin: 100% 0
    }

    .van-popover[data-popper-placement=left-start] .van-popover__arrow {
      top: var(--van-padding-md)
    }

    .van-popover[data-popper-placement=left-end] {
      transform-origin: 100% 100%
    }

    .van-popover[data-popper-placement=left-end] .van-popover__arrow {
      bottom: var(--van-padding-md)
    }

    .van-popover[data-popper-placement^=right] .van-popover__arrow {
      left: 0;
      border-right-color: currentColor;
      border-left-width: 0;
      margin-left: calc(var(--van-popover-arrow-size) * -1)
    }

    .van-popover[data-popper-placement=right] {
      transform-origin: 0 50%
    }

    .van-popover[data-popper-placement=right] .van-popover__arrow {
      top: 50%;
      transform: translateY(-50%)
    }

    .van-popover[data-popper-placement=right-start] {
      transform-origin: 0 0
    }

    .van-popover[data-popper-placement=right-start] .van-popover__arrow {
      top: var(--van-padding-md)
    }

    .van-popover[data-popper-placement=right-end] {
      transform-origin: 0 100%
    }

    .van-popover[data-popper-placement=right-end] .van-popover__arrow {
      bottom: var(--van-padding-md)
    }

    .van-popover[data-popper-placement^=bottom] .van-popover__arrow {
      top: 0;
      border-top-width: 0;
      border-bottom-color: currentColor;
      margin-top: calc(var(--van-popover-arrow-size) * -1)
    }

    .van-popover[data-popper-placement=bottom] {
      transform-origin: 50% 0
    }

    .van-popover[data-popper-placement=bottom] .van-popover__arrow {
      left: 50%;
      transform: translate(-50%)
    }

    .van-popover[data-popper-placement=bottom-start] {
      transform-origin: 0 0
    }

    .van-popover[data-popper-placement=bottom-start] .van-popover__arrow {
      left: var(--van-padding-md)
    }

    .van-popover[data-popper-placement=bottom-end] {
      transform-origin: 100% 0
    }

    .van-popover[data-popper-placement=bottom-end] .van-popover__arrow {
      right: var(--van-padding-md)
    }

    .van-popover--light {
      color: var(--van-popover-light-text-color)
    }

    .van-popover--light .van-popover__content {
      background: var(--van-popover-light-background);
      box-shadow: 0 2px 12px rgba(50, 50, 51, .12)
    }

    .van-popover--light .van-popover__arrow {
      color: var(--van-popover-light-background)
    }

    .van-popover--light .van-popover__action:active {
      background-color: var(--van-active-color)
    }

    .van-popover--light .van-popover__action--disabled {
      color: var(--van-popover-light-action-disabled-text-color);
      cursor: not-allowed
    }

    .van-popover--light .van-popover__action--disabled:active {
      background-color: transparent
    }

    .van-popover--dark {
      color: var(--van-popover-dark-text-color)
    }

    .van-popover--dark .van-popover__content {
      background: var(--van-popover-dark-background)
    }

    .van-popover--dark .van-popover__arrow {
      color: var(--van-popover-dark-background)
    }

    .van-popover--dark .van-popover__action:active {
      background-color: rgba(0, 0, 0, .2)
    }

    .van-popover--dark .van-popover__action--disabled {
      color: var(--van-popover-dark-action-disabled-text-color)
    }

    .van-popover--dark .van-popover__action--disabled:active {
      background-color: transparent
    }

    .van-popover--dark .van-popover__action-text:after {
      border-color: var(--van-gray-7)
    }

    .van-popover-zoom-enter-from,
    .van-popover-zoom-leave-active {
      transform: scale(.8);
      opacity: 0
    }

    .van-popover-zoom-enter-active {
      transition-timing-function: var(--van-ease-out)
    }

    .van-popover-zoom-leave-active {
      transition-timing-function: var(--van-ease-in)
    }

    :root {
      --van-notify-text-color: var(--van-white);
      --van-notify-padding: var(--van-padding-xs) var(--van-padding-md);
      --van-notify-font-size: var(--van-font-size-md);
      --van-notify-line-height: var(--van-line-height-md);
      --van-notify-primary-background: var(--van-primary-color);
      --van-notify-success-background: var(--van-success-color);
      --van-notify-danger-background: var(--van-danger-color);
      --van-notify-warning-background: var(--van-warning-color)
    }

    .van-notify {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      padding: var(--van-notify-padding);
      color: var(--van-notify-text-color);
      font-size: var(--van-notify-font-size);
      line-height: var(--van-notify-line-height);
      white-space: pre-wrap;
      text-align: center;
      word-wrap: break-word
    }

    .van-notify--primary {
      background: var(--van-notify-primary-background)
    }

    .van-notify--success {
      background: var(--van-notify-success-background)
    }

    .van-notify--danger {
      background: var(--van-notify-danger-background)
    }

    .van-notify--warning {
      background: var(--van-notify-warning-background)
    }

    :root {
      --van-dialog-width: 320px;
      --van-dialog-small-screen-width: 90%;
      --van-dialog-font-size: var(--van-font-size-lg);
      --van-dialog-transition: var(--van-duration-base);
      --van-dialog-radius: 16px;
      --van-dialog-background: var(--van-background-2);
      --van-dialog-header-font-weight: var(--van-font-bold);
      --van-dialog-header-line-height: 24px;
      --van-dialog-header-padding-top: 26px;
      --van-dialog-header-isolated-padding: var(--van-padding-lg) 0;
      --van-dialog-message-padding: var(--van-padding-lg);
      --van-dialog-message-font-size: var(--van-font-size-md);
      --van-dialog-message-line-height: var(--van-line-height-md);
      --van-dialog-message-max-height: 60vh;
      --van-dialog-has-title-message-text-color: var(--van-gray-7);
      --van-dialog-has-title-message-padding-top: var(--van-padding-xs);
      --van-dialog-button-height: 48px;
      --van-dialog-round-button-height: 36px;
      --van-dialog-confirm-button-text-color: var(--van-primary-color)
    }

    .van-dialog {
      top: 45%;
      width: var(--van-dialog-width);
      overflow: hidden;
      font-size: var(--van-dialog-font-size);
      background: var(--van-dialog-background);
      border-radius: var(--van-dialog-radius);
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      transition: var(--van-dialog-transition);
      transition-property: transform, opacity
    }

    @media (max-width: 321px) {
      .van-dialog {
        width: var(--van-dialog-small-screen-width)
      }
    }

    .van-dialog__header {
      color: var(--van-text-color);
      padding-top: var(--van-dialog-header-padding-top);
      font-weight: var(--van-dialog-header-font-weight);
      line-height: var(--van-dialog-header-line-height);
      text-align: center
    }

    .van-dialog__header--isolated {
      padding: var(--van-dialog-header-isolated-padding)
    }

    .van-dialog__content--isolated {
      display: flex;
      align-items: center;
      min-height: 104px
    }

    .van-dialog__message {
      color: var(--van-text-color);
      flex: 1;
      max-height: var(--van-dialog-message-max-height);
      padding: 26px var(--van-dialog-message-padding);
      overflow-y: auto;
      font-size: var(--van-dialog-message-font-size);
      line-height: var(--van-dialog-message-line-height);
      white-space: pre-wrap;
      text-align: center;
      word-wrap: break-word;
      -webkit-overflow-scrolling: touch
    }

    .van-dialog__message--has-title {
      padding-top: var(--van-dialog-has-title-message-padding-top);
      color: var(--van-dialog-has-title-message-text-color)
    }

    .van-dialog__message--left {
      text-align: left
    }

    .van-dialog__message--right {
      text-align: right
    }

    .van-dialog__message--justify {
      text-align: justify
    }

    .van-dialog__footer {
      display: flex;
      overflow: hidden;
      -webkit-user-select: none;
      user-select: none
    }

    .van-dialog__confirm,
    .van-dialog__cancel {
      flex: 1;
      height: var(--van-dialog-button-height);
      margin: 0;
      border: 0;
      border-radius: 0
    }

    .van-dialog__confirm,
    .van-dialog__confirm:active {
      color: var(--van-dialog-confirm-button-text-color)
    }

    .van-dialog--round-button .van-dialog__footer {
      position: relative;
      height: auto;
      padding: var(--van-padding-xs) var(--van-padding-lg) var(--van-padding-md)
    }

    .van-dialog--round-button .van-dialog__message {
      padding-bottom: var(--van-padding-md);
      color: var(--van-text-color)
    }

    .van-dialog--round-button .van-dialog__confirm,
    .van-dialog--round-button .van-dialog__cancel {
      height: var(--van-dialog-round-button-height)
    }

    .van-dialog--round-button .van-dialog__confirm {
      color: var(--van-white)
    }

    .van-dialog--round-button .van-action-bar-button--first {
      border-top-left-radius: var(--van-radius-max);
      border-bottom-left-radius: var(--van-radius-max)
    }

    .van-dialog--round-button .van-action-bar-button--last {
      border-top-right-radius: var(--van-radius-max);
      border-bottom-right-radius: var(--van-radius-max)
    }

    .van-dialog-bounce-enter-from {
      transform: translate3d(0, -50%, 0) scale(.7);
      opacity: 0
    }

    .van-dialog-bounce-leave-active {
      transform: translate3d(0, -50%, 0) scale(.9);
      opacity: 0
    }

    :root {
      --van-toast-max-width: 70%;
      --van-toast-font-size: var(--van-font-size-md);
      --van-toast-text-color: var(--van-white);
      --van-toast-loading-icon-color: var(--van-white);
      --van-toast-line-height: var(--van-line-height-md);
      --van-toast-radius: var(--van-radius-lg);
      --van-toast-background: rgba(0, 0, 0, .7);
      --van-toast-icon-size: 36px;
      --van-toast-text-min-width: 96px;
      --van-toast-text-padding: var(--van-padding-xs) var(--van-padding-sm);
      --van-toast-default-padding: var(--van-padding-md);
      --van-toast-default-width: 88px;
      --van-toast-default-min-height: 88px;
      --van-toast-position-top-distance: 20%;
      --van-toast-position-bottom-distance: 20%
    }

    .van-toast {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-sizing: content-box;
      transition: all var(--van-duration-fast);
      width: var(--van-toast-default-width);
      max-width: var(--van-toast-max-width);
      min-height: var(--van-toast-default-min-height);
      padding: var(--van-toast-default-padding);
      color: var(--van-toast-text-color);
      font-size: var(--van-toast-font-size);
      line-height: var(--van-toast-line-height);
      white-space: pre-wrap;
      word-break: break-all;
      text-align: center;
      background: var(--van-toast-background);
      border-radius: var(--van-toast-radius)
    }

    .van-toast--break-normal {
      word-break: normal;
      word-wrap: normal
    }

    .van-toast--break-word {
      word-break: normal;
      word-wrap: break-word
    }

    .van-toast--unclickable {
      overflow: hidden;
      cursor: not-allowed
    }

    .van-toast--unclickable * {
      pointer-events: none
    }

    .van-toast--text,
    .van-toast--html {
      width: -webkit-fit-content;
      width: fit-content;
      min-width: var(--van-toast-text-min-width);
      min-height: 0;
      padding: var(--van-toast-text-padding)
    }

    .van-toast--text .van-toast__text,
    .van-toast--html .van-toast__text {
      margin-top: 0
    }

    .van-toast--top {
      top: var(--van-toast-position-top-distance)
    }

    .van-toast--bottom {
      top: auto;
      bottom: var(--van-toast-position-bottom-distance)
    }

    .van-toast__icon {
      font-size: var(--van-toast-icon-size)
    }

    .van-toast__loading {
      padding: var(--van-padding-base);
      color: var(--van-toast-loading-icon-color)
    }

    .van-toast__text {
      margin-top: var(--van-padding-xs)
    }

    :root {
      --van-action-sheet-max-height: 80%;
      --van-action-sheet-header-height: 48px;
      --van-action-sheet-header-font-size: var(--van-font-size-lg);
      --van-action-sheet-description-color: var(--van-text-color-2);
      --van-action-sheet-description-font-size: var(--van-font-size-md);
      --van-action-sheet-description-line-height: var(--van-line-height-md);
      --van-action-sheet-item-background: var(--van-background-2);
      --van-action-sheet-item-font-size: var(--van-font-size-lg);
      --van-action-sheet-item-line-height: var(--van-line-height-lg);
      --van-action-sheet-item-text-color: var(--van-text-color);
      --van-action-sheet-item-disabled-text-color: var(--van-text-color-3);
      --van-action-sheet-subname-color: var(--van-text-color-2);
      --van-action-sheet-subname-font-size: var(--van-font-size-sm);
      --van-action-sheet-subname-line-height: var(--van-line-height-sm);
      --van-action-sheet-close-icon-size: 22px;
      --van-action-sheet-close-icon-color: var(--van-gray-5);
      --van-action-sheet-close-icon-padding: 0 var(--van-padding-md);
      --van-action-sheet-cancel-text-color: var(--van-gray-7);
      --van-action-sheet-cancel-padding-top: var(--van-padding-xs);
      --van-action-sheet-cancel-padding-color: var(--van-background);
      --van-action-sheet-loading-icon-size: 22px
    }

    .van-action-sheet {
      display: flex;
      flex-direction: column;
      max-height: var(--van-action-sheet-max-height);
      overflow: hidden;
      color: var(--van-action-sheet-item-text-color)
    }

    .van-action-sheet__content {
      flex: 1 auto;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch
    }

    .van-action-sheet__item,
    .van-action-sheet__cancel {
      display: block;
      width: 100%;
      padding: 14px var(--van-padding-md);
      font-size: var(--van-action-sheet-item-font-size);
      background: var(--van-action-sheet-item-background);
      border: none;
      cursor: pointer
    }

    .van-action-sheet__item:active,
    .van-action-sheet__cancel:active {
      background-color: var(--van-active-color)
    }

    .van-action-sheet__item {
      line-height: var(--van-action-sheet-item-line-height)
    }

    .van-action-sheet__item--loading,
    .van-action-sheet__item--disabled {
      color: var(--van-action-sheet-item-disabled-text-color)
    }

    .van-action-sheet__item--loading:active,
    .van-action-sheet__item--disabled:active {
      background-color: var(--van-action-sheet-item-background)
    }

    .van-action-sheet__item--disabled {
      cursor: not-allowed
    }

    .van-action-sheet__item--loading {
      cursor: default
    }

    .van-action-sheet__cancel {
      flex-shrink: 0;
      box-sizing: border-box;
      color: var(--van-action-sheet-cancel-text-color)
    }

    .van-action-sheet__subname {
      margin-top: var(--van-padding-xs);
      color: var(--van-action-sheet-subname-color);
      font-size: var(--van-action-sheet-subname-font-size);
      line-height: var(--van-action-sheet-subname-line-height)
    }

    .van-action-sheet__gap {
      display: block;
      height: var(--van-action-sheet-cancel-padding-top);
      background: var(--van-action-sheet-cancel-padding-color)
    }

    .van-action-sheet__header {
      flex-shrink: 0;
      font-weight: var(--van-font-bold);
      font-size: var(--van-action-sheet-header-font-size);
      line-height: var(--van-action-sheet-header-height);
      text-align: center
    }

    .van-action-sheet__description {
      position: relative;
      flex-shrink: 0;
      padding: 20px var(--van-padding-md);
      color: var(--van-action-sheet-description-color);
      font-size: var(--van-action-sheet-description-font-size);
      line-height: var(--van-action-sheet-description-line-height);
      text-align: center
    }

    .van-action-sheet__description:after {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      right: var(--van-padding-md);
      bottom: 0;
      left: var(--van-padding-md);
      border-bottom: 1px solid var(--van-border-color);
      transform: scaleY(.5)
    }

    .van-action-sheet__loading-icon .van-loading__spinner {
      width: var(--van-action-sheet-loading-icon-size);
      height: var(--van-action-sheet-loading-icon-size)
    }

    .van-action-sheet__close {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
      padding: var(--van-action-sheet-close-icon-padding);
      color: var(--van-action-sheet-close-icon-color);
      font-size: var(--van-action-sheet-close-icon-size);
      line-height: inherit
    }

    :root {
      --van-sticky-z-index: 99
    }

    .van-sticky--fixed {
      position: fixed;
      z-index: var(--van-sticky-z-index)
    }

    :root {
      --van-swipe-indicator-size: 6px;
      --van-swipe-indicator-margin: var(--van-padding-sm);
      --van-swipe-indicator-active-opacity: 1;
      --van-swipe-indicator-inactive-opacity: .3;
      --van-swipe-indicator-active-background: var(--van-primary-color);
      --van-swipe-indicator-inactive-background: var(--van-border-color)
    }

    .van-swipe {
      position: relative;
      overflow: hidden;
      transform: translateZ(0);
      cursor: grab;
      -webkit-user-select: none;
      user-select: none
    }

    .van-swipe__track {
      display: flex;
      height: 100%
    }

    .van-swipe__track--vertical {
      flex-direction: column
    }

    .van-swipe__indicators {
      position: absolute;
      bottom: var(--van-swipe-indicator-margin);
      left: 50%;
      display: flex;
      transform: translate(-50%)
    }

    .van-swipe__indicators--vertical {
      top: 50%;
      bottom: auto;
      left: var(--van-swipe-indicator-margin);
      flex-direction: column;
      transform: translateY(-50%)
    }

    .van-swipe__indicators--vertical .van-swipe__indicator:not(:last-child) {
      margin-bottom: var(--van-swipe-indicator-size)
    }

    .van-swipe__indicator {
      width: var(--van-swipe-indicator-size);
      height: var(--van-swipe-indicator-size);
      background-color: var(--van-swipe-indicator-inactive-background);
      border-radius: 100%;
      opacity: var(--van-swipe-indicator-inactive-opacity);
      transition: opacity var(--van-duration-fast), background-color var(--van-duration-fast)
    }

    .van-swipe__indicator:not(:last-child) {
      margin-right: var(--van-swipe-indicator-size)
    }

    .van-swipe__indicator--active {
      background-color: var(--van-swipe-indicator-active-background);
      opacity: var(--van-swipe-indicator-active-opacity)
    }

    .van-swipe-item {
      position: relative;
      flex-shrink: 0;
      width: 100%;
      height: 100%
    }

    :root {
      --van-image-preview-index-text-color: var(--van-white);
      --van-image-preview-index-font-size: var(--van-font-size-md);
      --van-image-preview-index-line-height: var(--van-line-height-md);
      --van-image-preview-index-text-shadow: 0 1px 1px var(--van-gray-8);
      --van-image-preview-overlay-background: rgba(0, 0, 0, .9);
      --van-image-preview-close-icon-size: 22px;
      --van-image-preview-close-icon-color: var(--van-gray-5);
      --van-image-preview-close-icon-margin: var(--van-padding-md);
      --van-image-preview-close-icon-z-index: 1
    }

    .van-image-preview {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      max-width: none;
      background-color: transparent;
      transform: none
    }

    .van-image-preview__swipe {
      height: 100%
    }

    .van-image-preview__swipe-item {
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden
    }

    .van-image-preview__cover {
      position: absolute;
      top: 0;
      left: 0
    }

    .van-image-preview__image,
    .van-image-preview__image-wrap {
      width: 100%;
      transition-property: transform
    }

    .van-image-preview__image--vertical,
    .van-image-preview__image-wrap--vertical {
      width: auto;
      height: 100%
    }

    .van-image-preview__image img,
    .van-image-preview__image-wrap img,
    .van-image-preview__image uni-video,
    .van-image-preview__image-wrap uni-video {
      -webkit-user-drag: none
    }

    .van-image-preview__image .van-image__error,
    .van-image-preview__image-wrap .van-image__error {
      top: 30%;
      height: 40%
    }

    .van-image-preview__image .van-image__error-icon,
    .van-image-preview__image-wrap .van-image__error-icon {
      font-size: 36px
    }

    .van-image-preview__image .van-image__loading,
    .van-image-preview__image-wrap .van-image__loading {
      background-color: transparent
    }

    .van-image-preview__index {
      position: absolute;
      top: var(--van-padding-md);
      left: 50%;
      color: var(--van-image-preview-index-text-color);
      font-size: var(--van-image-preview-index-font-size);
      line-height: var(--van-image-preview-index-line-height);
      text-shadow: var(--van-image-preview-index-text-shadow);
      transform: translate(-50%)
    }

    .van-image-preview__overlay {
      background: var(--van-image-preview-overlay-background)
    }

    .van-image-preview__close-icon {
      position: absolute;
      z-index: var(--van-image-preview-close-icon-z-index);
      color: var(--van-image-preview-close-icon-color);
      font-size: var(--van-image-preview-close-icon-size)
    }

    .van-image-preview__close-icon--top-left {
      top: var(--van-image-preview-close-icon-margin);
      left: var(--van-image-preview-close-icon-margin)
    }

    .van-image-preview__close-icon--top-right {
      top: var(--van-image-preview-close-icon-margin);
      right: var(--van-image-preview-close-icon-margin)
    }

    .van-image-preview__close-icon--bottom-left {
      bottom: var(--van-image-preview-close-icon-margin);
      left: var(--van-image-preview-close-icon-margin)
    }

    .van-image-preview__close-icon--bottom-right {
      right: var(--van-image-preview-close-icon-margin);
      bottom: var(--van-image-preview-close-icon-margin)
    }

    :root {
      --van-uploader-size: 80px;
      --van-uploader-icon-size: 24px;
      --van-uploader-icon-color: var(--van-gray-4);
      --van-uploader-text-color: var(--van-text-color-2);
      --van-uploader-text-font-size: var(--van-font-size-sm);
      --van-uploader-upload-background: var(--van-gray-1);
      --van-uploader-upload-active-color: var(--van-active-color);
      --van-uploader-delete-color: var(--van-white);
      --van-uploader-delete-icon-size: 14px;
      --van-uploader-delete-background: rgba(0, 0, 0, .7);
      --van-uploader-file-background: var(--van-background);
      --van-uploader-file-icon-size: 20px;
      --van-uploader-file-icon-color: var(--van-gray-7);
      --van-uploader-file-name-padding: 0 var(--van-padding-base);
      --van-uploader-file-name-margin-top: var(--van-padding-xs);
      --van-uploader-file-name-font-size: var(--van-font-size-sm);
      --van-uploader-file-name-text-color: var(--van-gray-7);
      --van-uploader-mask-text-color: var(--van-white);
      --van-uploader-mask-background: rgba(50, 50, 51, .88);
      --van-uploader-mask-icon-size: 22px;
      --van-uploader-mask-message-font-size: var(--van-font-size-sm);
      --van-uploader-mask-message-line-height: var(--van-line-height-xs);
      --van-uploader-loading-icon-size: 22px;
      --van-uploader-loading-icon-color: var(--van-white);
      --van-uploader-disabled-opacity: var(--van-disabled-opacity)
    }

    .van-uploader {
      position: relative;
      display: inline-block
    }

    .van-uploader__wrapper {
      display: flex;
      flex-wrap: wrap
    }

    .van-uploader__wrapper--disabled {
      opacity: var(--van-uploader-disabled-opacity)
    }

    .van-uploader__input {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      cursor: pointer;
      opacity: 0
    }

    .van-uploader__input-wrapper {
      position: relative
    }

    .van-uploader__input:disabled {
      cursor: not-allowed
    }

    .van-uploader__upload {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: var(--van-uploader-size);
      height: var(--van-uploader-size);
      margin: 0 var(--van-padding-xs) var(--van-padding-xs) 0;
      background: var(--van-uploader-upload-background)
    }

    .van-uploader__upload:active {
      background-color: var(--van-uploader-upload-active-color)
    }

    .van-uploader__upload--readonly:active {
      background-color: var(--van-uploader-upload-background)
    }

    .van-uploader__upload-icon {
      color: var(--van-uploader-icon-color);
      font-size: var(--van-uploader-icon-size)
    }

    .van-uploader__upload-text {
      margin-top: var(--van-padding-xs);
      color: var(--van-uploader-text-color);
      font-size: var(--van-uploader-text-font-size)
    }

    .van-uploader__preview {
      position: relative;
      margin: 0 var(--van-padding-xs) var(--van-padding-xs) 0;
      cursor: pointer
    }

    .van-uploader__preview-image {
      display: block;
      width: var(--van-uploader-size);
      height: var(--van-uploader-size);
      overflow: hidden
    }

    .van-uploader__preview-delete {
      position: absolute;
      top: 0;
      right: 0
    }

    .van-uploader__preview-delete--shadow {
      width: var(--van-uploader-delete-icon-size);
      height: var(--van-uploader-delete-icon-size);
      background: var(--van-uploader-delete-background);
      border-radius: 0 0 0 12px
    }

    .van-uploader__preview-delete-icon {
      position: absolute;
      top: 0;
      right: 0;
      color: var(--van-uploader-delete-color);
      font-size: var(--van-uploader-delete-icon-size);
      transform: scale(.7) translate(10%, -10%)
    }

    .van-uploader__preview-cover {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0
    }

    .van-uploader__mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--van-uploader-mask-text-color);
      background: var(--van-uploader-mask-background)
    }

    .van-uploader__mask-icon {
      font-size: var(--van-uploader-mask-icon-size)
    }

    .van-uploader__mask-message {
      margin-top: 6px;
      padding: 0 var(--van-padding-base);
      font-size: var(--van-uploader-mask-message-font-size);
      line-height: var(--van-uploader-mask-message-line-height)
    }

    .van-uploader__loading {
      width: var(--van-uploader-loading-icon-size);
      height: var(--van-uploader-loading-icon-size);
      color: var(--van-uploader-loading-icon-color)
    }

    .van-uploader__file {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: var(--van-uploader-size);
      height: var(--van-uploader-size);
      background: var(--van-uploader-file-background)
    }

    .van-uploader__file-icon {
      color: var(--van-uploader-file-icon-color);
      font-size: var(--van-uploader-file-icon-size)
    }

    .van-uploader__file-name {
      box-sizing: border-box;
      width: 100%;
      margin-top: var(--van-uploader-file-name-margin-top);
      padding: var(--van-uploader-file-name-padding);
      color: var(--van-uploader-file-name-text-color);
      font-size: var(--van-uploader-file-name-font-size);
      text-align: center
    }

    :root {
      --van-tab-text-color: var(--van-gray-7);
      --van-tab-active-text-color: var(--van-text-color);
      --van-tab-disabled-text-color: var(--van-text-color-3);
      --van-tab-font-size: var(--van-font-size-md);
      --van-tab-line-height: var(--van-line-height-md);
      --van-tabs-default-color: var(--van-primary-color);
      --van-tabs-line-height: 44px;
      --van-tabs-card-height: 30px;
      --van-tabs-nav-background: var(--van-background-2);
      --van-tabs-bottom-bar-width: 40px;
      --van-tabs-bottom-bar-height: 3px;
      --van-tabs-bottom-bar-color: var(--van-primary-color)
    }

    .van-tab {
      position: relative;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      padding: 0 var(--van-padding-base);
      color: var(--van-tab-text-color);
      font-size: var(--van-tab-font-size);
      line-height: var(--van-tab-line-height);
      cursor: pointer
    }

    .van-tab--active {
      color: var(--van-tab-active-text-color);
      font-weight: var(--van-font-bold)
    }

    .van-tab--disabled {
      color: var(--van-tab-disabled-text-color);
      cursor: not-allowed
    }

    .van-tab--grow {
      flex: 1 0 auto;
      padding: 0 var(--van-padding-sm)
    }

    .van-tab--shrink {
      flex: none;
      padding: 0 var(--van-padding-xs)
    }

    .van-tab--card {
      color: var(--van-tabs-default-color);
      border-right: var(--van-border-width) solid var(--van-tabs-default-color)
    }

    .van-tab--card:last-child {
      border-right: none
    }

    .van-tab--card.van-tab--active {
      color: var(--van-white);
      background-color: var(--van-tabs-default-color)
    }

    .van-tab--card--disabled {
      color: var(--van-tab-disabled-text-color)
    }

    .van-tab__text--ellipsis {
      display: -webkit-box;
      overflow: hidden;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical
    }

    .van-tabs {
      position: relative
    }

    .van-tabs__wrap {
      overflow: hidden
    }

    .van-tabs__wrap--page-top {
      position: fixed
    }

    .van-tabs__wrap--content-bottom {
      top: auto;
      bottom: 0
    }

    .van-tabs__nav {
      position: relative;
      display: flex;
      background: var(--van-tabs-nav-background);
      -webkit-user-select: none;
      user-select: none
    }

    .van-tabs__nav--complete {
      overflow-x: auto;
      overflow-y: hidden;
      -webkit-overflow-scrolling: touch
    }

    .van-tabs__nav--complete::-webkit-scrollbar {
      display: none
    }

    .van-tabs__nav--line {
      box-sizing: content-box;
      height: 100%;
      padding-bottom: 15px
    }

    .van-tabs__nav--line.van-tabs__nav--shrink,
    .van-tabs__nav--line.van-tabs__nav--complete {
      padding-right: var(--van-padding-xs);
      padding-left: var(--van-padding-xs)
    }

    .van-tabs__nav--card {
      box-sizing: border-box;
      height: var(--van-tabs-card-height);
      margin: 0 var(--van-padding-md);
      border: var(--van-border-width) solid var(--van-tabs-default-color);
      border-radius: var(--van-border-radius-sm)
    }

    .van-tabs__nav--card.van-tabs__nav--shrink {
      display: inline-flex
    }

    .van-tabs__line {
      position: absolute;
      bottom: 15px;
      left: 0;
      z-index: 1;
      width: var(--van-tabs-bottom-bar-width);
      height: var(--van-tabs-bottom-bar-height);
      background: var(--van-tabs-bottom-bar-color);
      border-radius: var(--van-tabs-bottom-bar-height)
    }

    .van-tabs__track {
      position: relative;
      display: flex;
      width: 100%;
      height: 100%;
      will-change: left
    }

    .van-tabs__content--animated {
      overflow: hidden
    }

    .van-tabs--line .van-tabs__wrap {
      height: var(--van-tabs-line-height)
    }

    .van-tabs--card>.van-tabs__wrap {
      height: var(--van-tabs-card-height)
    }

    .van-tab__panel,
    .van-tab__panel-wrapper {
      flex-shrink: 0;
      box-sizing: border-box;
      width: 100%
    }

    .van-tab__panel-wrapper--inactive {
      height: 0;
      overflow: visible
    }

    :root {
      --van-cascader-header-height: 48px;
      --van-cascader-header-padding: 0 var(--van-padding-md);
      --van-cascader-title-font-size: var(--van-font-size-lg);
      --van-cascader-title-line-height: 20px;
      --van-cascader-close-icon-size: 22px;
      --van-cascader-close-icon-color: var(--van-gray-5);
      --van-cascader-selected-icon-size: 18px;
      --van-cascader-tabs-height: 48px;
      --van-cascader-active-color: var(--van-primary-color);
      --van-cascader-options-height: 384px;
      --van-cascader-option-disabled-color: var(--van-text-color-3);
      --van-cascader-tab-color: var(--van-text-color);
      --van-cascader-unselected-tab-color: var(--van-text-color-2)
    }

    .van-cascader__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: var(--van-cascader-header-height);
      padding: var(--van-cascader-header-padding)
    }

    .van-cascader__title {
      font-weight: var(--van-font-bold);
      font-size: var(--van-cascader-title-font-size);
      line-height: var(--van-cascader-title-line-height)
    }

    .van-cascader__close-icon {
      color: var(--van-cascader-close-icon-color);
      font-size: var(--van-cascader-close-icon-size)
    }

    .van-cascader__tabs.van-tabs--line .van-tabs__wrap {
      height: var(--van-cascader-tabs-height)
    }

    .van-cascader__tab {
      color: var(--van-cascader-tab-color);
      font-weight: var(--van-font-bold)
    }

    .van-cascader__tab--unselected {
      color: var(--van-cascader-unselected-tab-color);
      font-weight: 400
    }

    .van-cascader__option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px var(--van-padding-md);
      font-size: var(--van-font-size-md);
      line-height: var(--van-line-height-md);
      cursor: pointer
    }

    .van-cascader__option:active {
      background-color: var(--van-active-color)
    }

    .van-cascader__option--selected {
      color: var(--van-cascader-active-color);
      font-weight: var(--van-font-bold)
    }

    .van-cascader__option--disabled {
      color: var(--van-cascader-option-disabled-color);
      cursor: not-allowed
    }

    .van-cascader__option--disabled:active {
      background-color: transparent
    }

    .van-cascader__selected-icon {
      font-size: var(--van-cascader-selected-icon-size)
    }

    .van-cascader__options {
      box-sizing: border-box;
      height: var(--van-cascader-options-height);
      padding-top: 6px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch
    }

    :root {
      --van-picker-background: var(--van-background-2);
      --van-picker-toolbar-height: 44px;
      --van-picker-title-font-size: var(--van-font-size-lg);
      --van-picker-title-line-height: var(--van-line-height-md);
      --van-picker-action-padding: 0 var(--van-padding-md);
      --van-picker-action-font-size: var(--van-font-size-md);
      --van-picker-confirm-action-color: var(--van-primary-color);
      --van-picker-cancel-action-color: var(--van-text-color-2);
      --van-picker-option-font-size: var(--van-font-size-lg);
      --van-picker-option-padding: 0 var(--van-padding-base);
      --van-picker-option-text-color: var(--van-text-color);
      --van-picker-option-disabled-opacity: .3;
      --van-picker-loading-icon-color: var(--van-primary-color);
      --van-picker-loading-mask-color: rgba(255, 255, 255, .9);
      --van-picker-mask-color: linear-gradient(180deg, rgba(255, 255, 255, .9), rgba(255, 255, 255, .4)), linear-gradient(0deg, rgba(255, 255, 255, .9), rgba(255, 255, 255, .4))
    }

    .van-theme-dark {
      --van-picker-loading-mask-color: rgba(0, 0, 0, .6);
      --van-picker-mask-color: linear-gradient(180deg, rgba(0, 0, 0, .6), rgba(0, 0, 0, .1)), linear-gradient(0deg, rgba(0, 0, 0, .6), rgba(0, 0, 0, .1))
    }

    .van-picker {
      position: relative;
      background: var(--van-picker-background);
      -webkit-user-select: none;
      user-select: none
    }

    .van-picker__toolbar {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: var(--van-picker-toolbar-height)
    }

    .van-picker__cancel,
    .van-picker__confirm {
      height: 100%;
      padding: var(--van-picker-action-padding);
      font-size: var(--van-picker-action-font-size);
      background-color: transparent;
      border: none
    }

    .van-picker__confirm {
      color: var(--van-picker-confirm-action-color)
    }

    .van-picker__cancel {
      color: var(--van-picker-cancel-action-color)
    }

    .van-picker__title {
      position: absolute;
      left: 50%;
      color: var(--van-text-color);
      max-width: 50%;
      font-weight: var(--van-font-bold);
      font-size: var(--van-picker-title-font-size);
      line-height: var(--van-picker-title-line-height);
      text-align: center;
      transform: translate(-50%)
    }

    .van-picker__columns {
      position: relative;
      display: flex;
      cursor: grab
    }

    .van-picker__loading {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--van-picker-loading-icon-color);
      background: var(--van-picker-loading-mask-color)
    }

    .van-picker__frame {
      position: absolute;
      top: 50%;
      right: var(--van-padding-md);
      left: var(--van-padding-md);
      z-index: 2;
      transform: translateY(-50%);
      pointer-events: none
    }

    .van-picker__mask {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
      background-image: var(--van-picker-mask-color);
      background-repeat: no-repeat;
      background-position: top, bottom;
      transform: translateZ(0);
      pointer-events: none
    }

    .van-picker-column {
      flex: 1;
      overflow: hidden;
      font-size: var(--van-picker-option-font-size)
    }

    .van-picker-column__wrapper {
      transition-timing-function: cubic-bezier(.23, 1, .68, 1)
    }

    .van-picker-column__item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--van-picker-option-padding);
      color: var(--van-picker-option-text-color)
    }

    .van-picker-column__item--disabled {
      cursor: not-allowed;
      opacity: var(--van-picker-option-disabled-opacity)
    }

    :root {
      --van-picker-group-background: var(--van-background-2)
    }

    .van-picker-group {
      background: var(--van-picker-group-background)
    }

    .van-picker-group__tabs {
      margin-top: var(--van-padding-base)
    }

    .van-picker-group__tab-title {
      margin-right: 16px
    }

    :root {
      --van-calendar-background: var(--van-background-2);
      --van-calendar-popup-height: 80%;
      --van-calendar-header-shadow: 0 2px 10px rgba(125, 126, 128, .16);
      --van-calendar-header-title-height: 44px;
      --van-calendar-header-title-font-size: var(--van-font-size-lg);
      --van-calendar-header-subtitle-font-size: var(--van-font-size-md);
      --van-calendar-weekdays-height: 30px;
      --van-calendar-weekdays-font-size: var(--van-font-size-sm);
      --van-calendar-month-title-font-size: var(--van-font-size-md);
      --van-calendar-month-mark-color: rgba(242, 243, 245, .8);
      --van-calendar-month-mark-font-size: 160px;
      --van-calendar-day-height: 64px;
      --van-calendar-day-font-size: var(--van-font-size-lg);
      --van-calendar-day-margin-bottom: 4px;
      --van-calendar-range-edge-color: var(--van-white);
      --van-calendar-range-edge-background: var(--van-primary-color);
      --van-calendar-range-middle-color: var(--van-primary-color);
      --van-calendar-range-middle-background-opacity: .1;
      --van-calendar-selected-day-size: 54px;
      --van-calendar-selected-day-color: var(--van-white);
      --van-calendar-info-font-size: var(--van-font-size-xs);
      --van-calendar-info-line-height: var(--van-line-height-xs);
      --van-calendar-selected-day-background: var(--van-primary-color);
      --van-calendar-day-disabled-color: var(--van-text-color-3);
      --van-calendar-confirm-button-height: 36px;
      --van-calendar-confirm-button-margin: 7px 0
    }

    .van-theme-dark {
      --van-calendar-month-mark-color: rgba(100, 101, 102, .2);
      --van-calendar-day-disabled-color: var(--van-gray-7)
    }

    .van-calendar {
      display: flex;
      flex-direction: column;
      height: 100%;
      background: var(--van-calendar-background)
    }

    .van-calendar__popup.van-popup--top,
    .van-calendar__popup.van-popup--bottom {
      height: var(--van-calendar-popup-height)
    }

    .van-calendar__popup.van-popup--left,
    .van-calendar__popup.van-popup--right {
      height: 100%
    }

    .van-calendar__popup .van-popup__close-icon {
      top: 11px
    }

    .van-calendar__header {
      flex-shrink: 0;
      box-shadow: var(--van-calendar-header-shadow)
    }

    .van-calendar__month-title,
    .van-calendar__header-title,
    .van-calendar__header-subtitle {
      color: var(--van-text-color);
      height: var(--van-calendar-header-title-height);
      font-weight: var(--van-font-bold);
      line-height: var(--van-calendar-header-title-height);
      text-align: center
    }

    .van-calendar__header-title {
      font-size: var(--van-calendar-header-title-font-size)
    }

    .van-calendar__header-subtitle {
      font-size: var(--van-calendar-header-subtitle-font-size)
    }

    .van-calendar__month-title {
      font-size: var(--van-calendar-month-title-font-size)
    }

    .van-calendar__weekdays {
      display: flex
    }

    .van-calendar__weekday {
      flex: 1;
      font-size: var(--van-calendar-weekdays-font-size);
      line-height: var(--van-calendar-weekdays-height);
      text-align: center
    }

    .van-calendar__body {
      flex: 1;
      overflow: auto;
      -webkit-overflow-scrolling: touch
    }

    .van-calendar__days {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      -webkit-user-select: none;
      user-select: none
    }

    .van-calendar__month-mark {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 0;
      color: var(--van-calendar-month-mark-color);
      font-size: var(--van-calendar-month-mark-font-size);
      transform: translate(-50%, -50%);
      pointer-events: none
    }

    .van-calendar__day,
    .van-calendar__selected-day {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center
    }

    .van-calendar__day {
      position: relative;
      width: 14.285%;
      height: var(--van-calendar-day-height);
      font-size: var(--van-calendar-day-font-size);
      margin-bottom: var(--van-calendar-day-margin-bottom);
      cursor: pointer
    }

    .van-calendar__day--end,
    .van-calendar__day--start,
    .van-calendar__day--start-end,
    .van-calendar__day--multiple-middle,
    .van-calendar__day--multiple-selected {
      color: var(--van-calendar-range-edge-color);
      background: var(--van-calendar-range-edge-background)
    }

    .van-calendar__day--start {
      border-radius: var(--van-radius-md) 0 0 var(--van-radius-md)
    }

    .van-calendar__day--end {
      border-radius: 0 var(--van-radius-md) var(--van-radius-md) 0
    }

    .van-calendar__day--start-end,
    .van-calendar__day--multiple-selected {
      border-radius: var(--van-radius-md)
    }

    .van-calendar__day--middle {
      color: var(--van-calendar-range-middle-color)
    }

    .van-calendar__day--middle:after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background-color: currentColor;
      opacity: var(--van-calendar-range-middle-background-opacity);
      content: ""
    }

    .van-calendar__day--disabled {
      color: var(--van-calendar-day-disabled-color);
      cursor: default
    }

    .van-calendar__top-info,
    .van-calendar__bottom-info {
      position: absolute;
      right: 0;
      left: 0;
      font-size: var(--van-calendar-info-font-size);
      line-height: var(--van-calendar-info-line-height)
    }

    @media (max-width: 350px) {

      .van-calendar__top-info,
      .van-calendar__bottom-info {
        font-size: 9px
      }
    }

    .van-calendar__top-info {
      top: 6px
    }

    .van-calendar__bottom-info {
      bottom: 6px
    }

    .van-calendar__selected-day {
      width: var(--van-calendar-selected-day-size);
      height: var(--van-calendar-selected-day-size);
      color: var(--van-calendar-selected-day-color);
      background: var(--van-calendar-selected-day-background);
      border-radius: var(--van-radius-md)
    }

    .van-calendar__footer {
      flex-shrink: 0;
      padding-left: var(--van-padding-md);
      padding-right: var(--van-padding-md)
    }

    .van-calendar__confirm {
      height: var(--van-calendar-confirm-button-height);
      margin: var(--van-calendar-confirm-button-margin)
    }

    :root {
      --van-address-edit-padding: var(--van-padding-sm);
      --van-address-edit-buttons-padding: var(--van-padding-xl) var(--van-padding-base);
      --van-address-edit-button-margin-bottom: var(--van-padding-sm);
      --van-address-edit-button-font-size: var(--van-font-size-lg)
    }

    .van-address-edit {
      padding: var(--van-address-edit-padding)
    }

    .van-address-edit__fields {
      overflow: hidden;
      border-radius: var(--van-padding-xs)
    }

    .van-address-edit__fields .van-field__label {
      width: 4.1em
    }

    .van-address-edit__default {
      margin-top: var(--van-padding-sm);
      overflow: hidden;
      border-radius: var(--van-padding-xs)
    }

    .van-address-edit__buttons {
      padding: var(--van-address-edit-buttons-padding)
    }

    .van-address-edit__button {
      margin-bottom: var(--van-address-edit-button-margin-bottom);
      font-size: var(--van-address-edit-button-font-size)
    }

    .van-address-edit-detail__search-item {
      background: var(--van-gray-2)
    }

    .van-radio-group--horizontal,
    .van-checkbox-group--horizontal {
      display: flex;
      flex-wrap: wrap
    }

    :root {
      --van-checkbox-size: 20px;
      --van-checkbox-border-color: var(--van-gray-5);
      --van-checkbox-duration: var(--van-duration-fast);
      --van-checkbox-label-margin: var(--van-padding-xs);
      --van-checkbox-label-color: var(--van-text-color);
      --van-checkbox-checked-icon-color: var(--van-primary-color);
      --van-checkbox-disabled-icon-color: var(--van-gray-5);
      --van-checkbox-disabled-label-color: var(--van-text-color-3);
      --van-checkbox-disabled-background: var(--van-border-color)
    }

    .van-checkbox {
      display: flex;
      align-items: center;
      overflow: hidden;
      cursor: pointer;
      -webkit-user-select: none;
      user-select: none
    }

    .van-checkbox--disabled {
      cursor: not-allowed
    }

    .van-checkbox--label-disabled {
      cursor: default
    }

    .van-checkbox--horizontal {
      margin-right: var(--van-padding-sm)
    }

    .van-checkbox__icon {
      flex: none;
      height: 1em;
      font-size: var(--van-checkbox-size);
      line-height: 1em;
      cursor: pointer
    }

    .van-checkbox__icon .van-icon {
      display: block;
      box-sizing: border-box;
      width: 1.25em;
      height: 1.25em;
      color: transparent;
      font-size: .8em;
      line-height: 1.25;
      text-align: center;
      border: 1px solid var(--van-checkbox-border-color);
      transition-duration: var(--van-checkbox-duration);
      transition-property: color, border-color, background-color
    }

    .van-checkbox__icon--round .van-icon {
      border-radius: 100%
    }

    .van-checkbox__icon--checked .van-icon {
      color: var(--van-white);
      background-color: var(--van-checkbox-checked-icon-color);
      border-color: var(--van-checkbox-checked-icon-color)
    }

    .van-checkbox__icon--disabled {
      cursor: not-allowed
    }

    .van-checkbox__icon--disabled .van-icon {
      background-color: var(--van-checkbox-disabled-background);
      border-color: var(--van-checkbox-disabled-icon-color)
    }

    .van-checkbox__icon--disabled.van-checkbox__icon--checked .van-icon {
      color: var(--van-checkbox-disabled-icon-color)
    }

    .van-checkbox__label {
      margin-left: var(--van-checkbox-label-margin);
      color: var(--van-checkbox-label-color);
      line-height: var(--van-checkbox-size)
    }

    .van-checkbox__label--left {
      margin: 0 var(--van-checkbox-label-margin) 0 0
    }

    .van-checkbox__label--disabled {
      color: var(--van-checkbox-disabled-label-color)
    }

    :root {
      --van-coupon-margin: 0 var(--van-padding-sm) var(--van-padding-sm);
      --van-coupon-content-height: 84px;
      --van-coupon-content-padding: 14px 0;
      --van-coupon-content-text-color: var(--van-text-color);
      --van-coupon-background: var(--van-background-2);
      --van-coupon-active-background: var(--van-active-color);
      --van-coupon-radius: var(--van-radius-lg);
      --van-coupon-shadow: 0 0 4px rgba(0, 0, 0, .1);
      --van-coupon-head-width: 96px;
      --van-coupon-amount-color: var(--van-primary-color);
      --van-coupon-amount-font-size: 30px;
      --van-coupon-currency-font-size: 40%;
      --van-coupon-name-font-size: var(--van-font-size-md);
      --van-coupon-disabled-text-color: var(--van-text-color-2);
      --van-coupon-description-padding: var(--van-padding-xs) var(--van-padding-md);
      --van-coupon-description-border-color: var(--van-border-color);
      --van-coupon-checkbox-color: var(--van-primary-color)
    }

    .van-coupon {
      margin: var(--van-coupon-margin);
      overflow: hidden;
      background: var(--van-coupon-background);
      border-radius: var(--van-coupon-radius);
      box-shadow: var(--van-coupon-shadow)
    }

    .van-coupon:active {
      background-color: var(--van-coupon-active-background)
    }

    .van-coupon__content {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      min-height: var(--van-coupon-content-height);
      padding: var(--van-coupon-content-padding);
      color: var(--van-coupon-content-text-color)
    }

    .van-coupon__head {
      position: relative;
      min-width: var(--van-coupon-head-width);
      padding: 0 var(--van-padding-xs);
      color: var(--van-coupon-amount-color);
      text-align: center
    }

    .van-coupon__amount,
    .van-coupon__condition,
    .van-coupon__name,
    .van-coupon__valid {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis
    }

    .van-coupon__amount {
      margin-bottom: 6px;
      font-weight: var(--van-font-bold);
      font-size: var(--van-coupon-amount-font-size);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis
    }

    .van-coupon__amount span {
      font-size: var(--van-coupon-currency-font-size)
    }

    .van-coupon__amount span:not(:empty) {
      margin-left: 2px
    }

    .van-coupon__condition {
      font-size: var(--van-font-size-sm);
      line-height: 16px;
      white-space: pre-wrap
    }

    .van-coupon__body {
      position: relative;
      flex: 1
    }

    .van-coupon__name {
      margin-bottom: 10px;
      font-weight: var(--van-font-bold);
      font-size: var(--van-coupon-name-font-size);
      line-height: var(--van-line-height-md)
    }

    .van-coupon__valid {
      font-size: var(--van-font-size-sm)
    }

    .van-coupon__corner {
      position: absolute;
      top: 0;
      right: var(--van-padding-md);
      bottom: 0
    }

    .van-coupon__corner .van-checkbox__icon--checked .van-icon {
      background-color: var(--van-coupon-checkbox-color);
      border-color: var(--van-coupon-checkbox-color)
    }

    .van-coupon__description {
      padding: var(--van-coupon-description-padding);
      font-size: var(--van-font-size-sm);
      border-top: 1px dashed var(--van-coupon-description-border-color)
    }

    .van-coupon--disabled:active {
      background-color: var(--van-coupon-background)
    }

    .van-coupon--disabled .van-coupon-item__content {
      height: calc(var(--van-coupon-content-height) - 10px)
    }

    .van-coupon--disabled .van-coupon__head {
      color: inherit
    }

    :root {
      --van-radio-size: 20px;
      --van-radio-border-color: var(--van-gray-5);
      --van-radio-duration: var(--van-duration-fast);
      --van-radio-label-margin: var(--van-padding-xs);
      --van-radio-label-color: var(--van-text-color);
      --van-radio-checked-icon-color: var(--van-primary-color);
      --van-radio-disabled-icon-color: var(--van-gray-5);
      --van-radio-disabled-label-color: var(--van-text-color-3);
      --van-radio-disabled-background: var(--van-border-color)
    }

    .van-radio {
      display: flex;
      align-items: center;
      overflow: hidden;
      cursor: pointer;
      -webkit-user-select: none;
      user-select: none
    }

    .van-radio--disabled {
      cursor: not-allowed
    }

    .van-radio--label-disabled {
      cursor: default
    }

    .van-radio--horizontal {
      margin-right: var(--van-padding-sm)
    }

    .van-radio__icon {
      flex: none;
      height: 1em;
      font-size: var(--van-radio-size);
      line-height: 1em;
      cursor: pointer
    }

    .van-radio__icon .van-icon {
      display: block;
      box-sizing: border-box;
      width: 1.25em;
      height: 1.25em;
      color: transparent;
      font-size: .8em;
      line-height: 1.25;
      text-align: center;
      border: 1px solid var(--van-radio-border-color);
      transition-duration: var(--van-radio-duration);
      transition-property: color, border-color, background-color
    }

    .van-radio__icon--round .van-icon {
      border-radius: 100%
    }

    .van-radio__icon--checked .van-icon {
      color: var(--van-white);
      background-color: var(--van-radio-checked-icon-color);
      border-color: var(--van-radio-checked-icon-color)
    }

    .van-radio__icon--disabled {
      cursor: not-allowed
    }

    .van-radio__icon--disabled .van-icon {
      background-color: var(--van-radio-disabled-background);
      border-color: var(--van-radio-disabled-icon-color)
    }

    .van-radio__icon--disabled.van-radio__icon--checked .van-icon {
      color: var(--van-radio-disabled-icon-color)
    }

    .van-radio__label {
      margin-left: var(--van-radio-label-margin);
      color: var(--van-radio-label-color);
      line-height: var(--van-radio-size)
    }

    .van-radio__label--left {
      margin: 0 var(--van-radio-label-margin) 0 0
    }

    .van-radio__label--disabled {
      color: var(--van-radio-disabled-label-color)
    }

    :root {
      --van-contact-list-padding: var(--van-padding-sm) var(--van-padding-sm) 80px;
      --van-contact-list-edit-icon-size: 16px;
      --van-contact-list-add-button-z-index: 999;
      --van-contact-list-radio-color: var(--van-primary-color);
      --van-contact-list-item-padding: var(--van-padding-md)
    }

    .van-contact-list {
      box-sizing: border-box;
      height: 100%;
      padding: var(--van-contact-list-padding)
    }

    .van-contact-list__item {
      padding: var(--van-contact-list-item-padding)
    }

    .van-contact-list__item-title {
      display: flex;
      align-items: center;
      padding-right: var(--van-padding-xl);
      padding-left: var(--van-padding-xs)
    }

    .van-contact-list__item-tag {
      flex: none;
      margin-left: var(--van-padding-xs);
      padding-top: 0;
      padding-bottom: 0;
      line-height: 1.4em
    }

    .van-contact-list__group {
      box-sizing: border-box;
      height: 100%;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch;
      border-radius: var(--van-radius-lg)
    }

    .van-contact-list__edit {
      font-size: var(--van-contact-list-edit-icon-size)
    }

    .van-contact-list__radio .van-radio__icon--checked .van-icon {
      background-color: var(--van-contact-list-radio-color);
      border-color: var(--van-contact-list-radio-color)
    }

    .van-contact-list__bottom {
      position: fixed;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: var(--van-contact-list-add-button-z-index);
      padding-left: var(--van-padding-md);
      padding-right: var(--van-padding-md);
      background-color: var(--van-background-2)
    }

    .van-contact-list__add {
      height: 40px;
      margin: 5px 0
    }

    :root {
      --van-address-list-padding: var(--van-padding-sm) var(--van-padding-sm) 80px;
      --van-address-list-disabled-text-color: var(--van-text-color-2);
      --van-address-list-disabled-text-padding: calc(var(--van-padding-base) * 5) 0;
      --van-address-list-disabled-text-font-size: var(--van-font-size-md);
      --van-address-list-disabled-text-line-height: var(--van-line-height-md);
      --van-address-list-add-button-z-index: 999;
      --van-address-list-item-padding: var(--van-padding-sm);
      --van-address-list-item-text-color: var(--van-text-color);
      --van-address-list-item-disabled-text-color: var(--van-text-color-3);
      --van-address-list-item-font-size: 13px;
      --van-address-list-item-line-height: var(--van-line-height-sm);
      --van-address-list-radio-color: var(--van-primary-color);
      --van-address-list-edit-icon-size: 20px
    }

    .van-address-list {
      box-sizing: border-box;
      height: 100%;
      padding: var(--van-address-list-padding)
    }

    .van-address-list__bottom {
      position: fixed;
      bottom: 0;
      left: 0;
      z-index: var(--van-address-list-add-button-z-index);
      box-sizing: border-box;
      width: 100%;
      padding-left: var(--van-padding-md);
      padding-right: var(--van-padding-md);
      background-color: var(--van-background-2)
    }

    .van-address-list__add {
      height: 40px;
      margin: 5px 0
    }

    .van-address-list__disabled-text {
      padding: var(--van-address-list-disabled-text-padding);
      color: var(--van-address-list-disabled-text-color);
      font-size: var(--van-address-list-disabled-text-font-size);
      line-height: var(--van-address-list-disabled-text-line-height)
    }

    .van-address-item {
      padding: var(--van-address-list-item-padding);
      background-color: var(--van-background-2);
      border-radius: var(--van-radius-lg)
    }

    .van-address-item:not(:last-child) {
      margin-bottom: var(--van-padding-sm)
    }

    .van-address-item__title {
      padding-right: 44px
    }

    .van-address-item__name {
      display: flex;
      align-items: center;
      margin-bottom: var(--van-padding-xs);
      font-size: var(--van-font-size-lg);
      line-height: var(--van-line-height-lg)
    }

    .van-address-item__tag {
      flex: none;
      margin-left: var(--van-padding-xs);
      padding-top: 0;
      padding-bottom: 0;
      line-height: 1.4em
    }

    .van-address-item__address {
      color: var(--van-address-list-item-text-color);
      font-size: var(--van-address-list-item-font-size);
      line-height: var(--van-address-list-item-line-height)
    }

    .van-address-item--disabled .van-address-item__name,
    .van-address-item--disabled .van-address-item__address {
      color: var(--van-address-list-item-disabled-text-color)
    }

    .van-address-item__edit {
      position: absolute;
      top: 50%;
      right: var(--van-padding-md);
      color: var(--van-gray-6);
      font-size: var(--van-address-list-edit-icon-size);
      transform: translateY(-50%)
    }

    .van-address-item .van-cell {
      padding: 0
    }

    .van-address-item .van-radio__label {
      margin-left: var(--van-padding-sm)
    }

    .van-address-item .van-radio__icon--checked .van-icon {
      background-color: var(--van-address-list-radio-color);
      border-color: var(--van-address-list-radio-color)
    }

    :root {
      --van-barrage-font-size: 16px;
      --van-barrage-space: 10px;
      --van-barrage-font: inherit;
      --van-barrage-color: var(--van-white)
    }

    .van-barrage {
      position: relative;
      overflow: hidden
    }

    .van-barrage__item {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 99;
      padding-bottom: var(--van-barrage-space);
      opacity: .75;
      line-height: 1;
      font-size: var(--van-barrage-font-size);
      font-family: var(--van-barrage-font);
      font-weight: 700;
      white-space: nowrap;
      color: var(--van-barrage-color);
      text-shadow: 1px 0 1px #000000, 0 1px 1px #000000, 0 -1px 1px #000000, -1px 0 1px #000000;
      -webkit-user-select: none;
      user-select: none;
      will-change: transform;
      transform: translate(110%)
    }

    @keyframes van-barrage {
      0% {
        transform: translate(110%)
      }

      to {
        transform: translate(var(--move-distance))
      }
    }

    :root {
      --van-cell-group-background: var(--van-background-2);
      --van-cell-group-title-color: var(--van-text-color-2);
      --van-cell-group-title-padding: var(--van-padding-md) var(--van-padding-md);
      --van-cell-group-title-font-size: var(--van-font-size-md);
      --van-cell-group-title-line-height: 16px;
      --van-cell-group-inset-padding: 0 var(--van-padding-md);
      --van-cell-group-inset-radius: var(--van-radius-lg);
      --van-cell-group-inset-title-padding: var(--van-padding-md) var(--van-padding-md)
    }

    .van-cell-group {
      background: var(--van-cell-group-background)
    }

    .van-cell-group--inset {
      margin: var(--van-cell-group-inset-padding);
      border-radius: var(--van-cell-group-inset-radius);
      overflow: hidden
    }

    .van-cell-group__title {
      padding: var(--van-cell-group-title-padding);
      color: var(--van-cell-group-title-color);
      font-size: var(--van-cell-group-title-font-size);
      line-height: var(--van-cell-group-title-line-height)
    }

    .van-cell-group__title--inset {
      padding: var(--van-cell-group-inset-title-padding)
    }

    :root {
      --van-circle-size: 100px;
      --van-circle-color: var(--van-primary-color);
      --van-circle-layer-color: var(--van-white);
      --van-circle-text-color: var(--van-text-color);
      --van-circle-text-font-weight: var(--van-font-bold);
      --van-circle-text-font-size: var(--van-font-size-md);
      --van-circle-text-line-height: var(--van-line-height-md)
    }

    .van-circle {
      position: relative;
      display: inline-block;
      width: var(--van-circle-size);
      height: var(--van-circle-size);
      text-align: center
    }

    .van-circle svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%
    }

    .van-circle__layer {
      stroke: var(--van-circle-layer-color)
    }

    .van-circle__hover {
      fill: none;
      stroke: var(--van-circle-color);
      stroke-linecap: round
    }

    .van-circle__text {
      position: absolute;
      top: 50%;
      left: 0;
      box-sizing: border-box;
      width: 100%;
      padding: 0 var(--van-padding-base);
      color: var(--van-circle-text-color);
      font-weight: var(--van-circle-text-font-weight);
      font-size: var(--van-circle-text-font-size);
      line-height: var(--van-circle-text-line-height);
      transform: translateY(-50%)
    }

    .van-row {
      display: flex;
      flex-wrap: wrap
    }

    .van-row--nowrap {
      flex-wrap: nowrap
    }

    .van-row--justify-center {
      justify-content: center
    }

    .van-row--justify-end {
      justify-content: flex-end
    }

    .van-row--justify-space-between {
      justify-content: space-between
    }

    .van-row--justify-space-around {
      justify-content: space-around
    }

    .van-row--align-center {
      align-items: center
    }

    .van-row--align-bottom {
      align-items: flex-end
    }

    .van-col {
      display: block;
      box-sizing: border-box;
      min-height: 1px
    }

    .van-col--1 {
      flex: 0 0 4.16666667%;
      max-width: 4.16666667%
    }

    .van-col--offset-1 {
      margin-left: 4.16666667%
    }

    .van-col--2 {
      flex: 0 0 8.33333333%;
      max-width: 8.33333333%
    }

    .van-col--offset-2 {
      margin-left: 8.33333333%
    }

    .van-col--3 {
      flex: 0 0 12.5%;
      max-width: 12.5%
    }

    .van-col--offset-3 {
      margin-left: 12.5%
    }

    .van-col--4 {
      flex: 0 0 16.66666667%;
      max-width: 16.66666667%
    }

    .van-col--offset-4 {
      margin-left: 16.66666667%
    }

    .van-col--5 {
      flex: 0 0 20.83333333%;
      max-width: 20.83333333%
    }

    .van-col--offset-5 {
      margin-left: 20.83333333%
    }

    .van-col--6 {
      flex: 0 0 25%;
      max-width: 25%
    }

    .van-col--offset-6 {
      margin-left: 25%
    }

    .van-col--7 {
      flex: 0 0 29.16666667%;
      max-width: 29.16666667%
    }

    .van-col--offset-7 {
      margin-left: 29.16666667%
    }

    .van-col--8 {
      flex: 0 0 33.33333333%;
      max-width: 33.33333333%
    }

    .van-col--offset-8 {
      margin-left: 33.33333333%
    }

    .van-col--9 {
      flex: 0 0 37.5%;
      max-width: 37.5%
    }

    .van-col--offset-9 {
      margin-left: 37.5%
    }

    .van-col--10 {
      flex: 0 0 41.66666667%;
      max-width: 41.66666667%
    }

    .van-col--offset-10 {
      margin-left: 41.66666667%
    }

    .van-col--11 {
      flex: 0 0 45.83333333%;
      max-width: 45.83333333%
    }

    .van-col--offset-11 {
      margin-left: 45.83333333%
    }

    .van-col--12 {
      flex: 0 0 50%;
      max-width: 50%
    }

    .van-col--offset-12 {
      margin-left: 50%
    }

    .van-col--13 {
      flex: 0 0 54.16666667%;
      max-width: 54.16666667%
    }

    .van-col--offset-13 {
      margin-left: 54.16666667%
    }

    .van-col--14 {
      flex: 0 0 58.33333333%;
      max-width: 58.33333333%
    }

    .van-col--offset-14 {
      margin-left: 58.33333333%
    }

    .van-col--15 {
      flex: 0 0 62.5%;
      max-width: 62.5%
    }

    .van-col--offset-15 {
      margin-left: 62.5%
    }

    .van-col--16 {
      flex: 0 0 66.66666667%;
      max-width: 66.66666667%
    }

    .van-col--offset-16 {
      margin-left: 66.66666667%
    }

    .van-col--17 {
      flex: 0 0 70.83333333%;
      max-width: 70.83333333%
    }

    .van-col--offset-17 {
      margin-left: 70.83333333%
    }

    .van-col--18 {
      flex: 0 0 75%;
      max-width: 75%
    }

    .van-col--offset-18 {
      margin-left: 75%
    }

    .van-col--19 {
      flex: 0 0 79.16666667%;
      max-width: 79.16666667%
    }

    .van-col--offset-19 {
      margin-left: 79.16666667%
    }

    .van-col--20 {
      flex: 0 0 83.33333333%;
      max-width: 83.33333333%
    }

    .van-col--offset-20 {
      margin-left: 83.33333333%
    }

    .van-col--21 {
      flex: 0 0 87.5%;
      max-width: 87.5%
    }

    .van-col--offset-21 {
      margin-left: 87.5%
    }

    .van-col--22 {
      flex: 0 0 91.66666667%;
      max-width: 91.66666667%
    }

    .van-col--offset-22 {
      margin-left: 91.66666667%
    }

    .van-col--23 {
      flex: 0 0 95.83333333%;
      max-width: 95.83333333%
    }

    .van-col--offset-23 {
      margin-left: 95.83333333%
    }

    .van-col--24 {
      flex: 0 0 100%;
      max-width: 100%
    }

    .van-col--offset-24 {
      margin-left: 100%
    }

    :root {
      --van-count-down-text-color: var(--van-text-color);
      --van-count-down-font-size: var(--van-font-size-md);
      --van-count-down-line-height: var(--van-line-height-md)
    }

    .van-count-down {
      color: var(--van-count-down-text-color);
      font-size: var(--van-count-down-font-size);
      line-height: var(--van-count-down-line-height)
    }

    :root {
      --van-empty-padding: var(--van-padding-xl) 0;
      --van-empty-image-size: 160px;
      --van-empty-description-margin-top: var(--van-padding-md);
      --van-empty-description-padding: 0 60px;
      --van-empty-description-color: var(--van-text-color-2);
      --van-empty-description-font-size: var(--van-font-size-md);
      --van-empty-description-line-height: var(--van-line-height-md);
      --van-empty-bottom-margin-top: 24px
    }

    .van-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      padding: var(--van-empty-padding)
    }

    .van-empty__image {
      width: var(--van-empty-image-size);
      height: var(--van-empty-image-size)
    }

    .van-empty__image img {
      width: 100%;
      height: 100%
    }

    .van-empty__description {
      margin-top: var(--van-empty-description-margin-top);
      padding: var(--van-empty-description-padding);
      color: var(--van-empty-description-color);
      font-size: var(--van-empty-description-font-size);
      line-height: var(--van-empty-description-line-height)
    }

    .van-empty__bottom {
      margin-top: var(--van-empty-bottom-margin-top)
    }

    .van-theme-dark .van-empty {
      opacity: .5
    }

    :root {
      --van-coupon-list-background: var(--van-background);
      --van-coupon-list-field-padding: 5px 0 5px var(--van-padding-md);
      --van-coupon-list-exchange-button-height: 32px;
      --van-coupon-list-close-button-height: 40px;
      --van-coupon-list-empty-tip-color: var(--van-text-color-2);
      --van-coupon-list-empty-tip-font-size: var(--van-font-size-md);
      --van-coupon-list-empty-tip-line-height: var(--van-line-height-md)
    }

    .van-coupon-list {
      position: relative;
      height: 100%;
      background: var(--van-coupon-list-background)
    }

    .van-coupon-list__field {
      padding: var(--van-coupon-list-field-padding)
    }

    .van-coupon-list__field .van-field__body {
      height: 34px;
      padding-left: var(--van-padding-sm);
      line-height: 34px;
      background: var(--van-background);
      border-radius: var(--van-radius-max)
    }

    .van-coupon-list__field .van-field__body::-webkit-input-placeholder {
      color: var(--van-text-color-3)
    }

    .van-coupon-list__field .van-field__body::placeholder {
      color: var(--van-text-color-3)
    }

    .van-coupon-list__field .van-field__clear {
      margin-right: 0
    }

    .van-coupon-list__exchange-bar {
      display: flex;
      align-items: center;
      background-color: var(--van-background-2)
    }

    .van-coupon-list__exchange {
      flex: none;
      height: var(--van-coupon-list-exchange-button-height);
      font-size: var(--van-font-size-lg);
      line-height: calc(var(--van-coupon-list-exchange-button-height) - 2px);
      border: 0
    }

    .van-coupon-list .van-tabs__wrap {
      box-shadow: 0 6px 12px -12px var(--van-gray-6)
    }

    .van-coupon-list__list {
      box-sizing: border-box;
      padding: var(--van-padding-md) 0 var(--van-padding-lg);
      overflow-y: auto;
      -webkit-overflow-scrolling: touch
    }

    .van-coupon-list__list--with-bottom {
      padding-bottom: 50px
    }

    .van-coupon-list__bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 999;
      box-sizing: border-box;
      width: 100%;
      padding: 5px var(--van-padding-md);
      font-weight: var(--van-font-bold);
      background-color: var(--van-background-2)
    }

    .van-coupon-list__close {
      height: var(--van-coupon-list-close-button-height)
    }

    .van-coupon-list__empty-tip {
      color: var(--van-coupon-list-empty-tip-color);
      font-size: var(--van-coupon-list-empty-tip-font-size);
      line-height: var(--van-coupon-list-empty-tip-line-height)
    }

    :root {
      --van-divider-margin: var(--van-padding-md) 0;
      --van-divider-vertical-margin: 0 var(--van-padding-xs);
      --van-divider-text-color: var(--van-text-color-2);
      --van-divider-font-size: var(--van-font-size-md);
      --van-divider-line-height: 24px;
      --van-divider-border-color: var(--van-border-color);
      --van-divider-content-padding: var(--van-padding-md);
      --van-divider-content-left-width: 10%;
      --van-divider-content-right-width: 10%
    }

    .van-divider {
      display: flex;
      align-items: center;
      margin: var(--van-divider-margin);
      color: var(--van-divider-text-color);
      font-size: var(--van-divider-font-size);
      line-height: var(--van-divider-line-height);
      border-color: var(--van-divider-border-color);
      border-style: solid;
      border-width: 0
    }

    .van-divider:before,
    .van-divider:after {
      display: block;
      flex: 1;
      box-sizing: border-box;
      height: 1px;
      border-color: inherit;
      border-style: inherit;
      border-width: var(--van-border-width) 0 0
    }

    .van-divider:before {
      content: ""
    }

    .van-divider--hairline:before,
    .van-divider--hairline:after {
      transform: scaleY(.5)
    }

    .van-divider--dashed {
      border-style: dashed
    }

    .van-divider--content-center:before,
    .van-divider--content-left:before,
    .van-divider--content-right:before {
      margin-right: var(--van-divider-content-padding)
    }

    .van-divider--content-center:after,
    .van-divider--content-left:after,
    .van-divider--content-right:after {
      margin-left: var(--van-divider-content-padding);
      content: ""
    }

    .van-divider--content-left:before {
      max-width: var(--van-divider-content-left-width)
    }

    .van-divider--content-right:after {
      max-width: var(--van-divider-content-right-width)
    }

    .van-divider--vertical {
      display: inline-block;
      width: var(--van-border-width);
      height: 1em;
      margin: var(--van-divider-vertical-margin);
      vertical-align: middle
    }

    .van-divider--vertical:before {
      height: 100%;
      border-width: 0 0 0 var(--van-border-width)
    }

    .van-divider--vertical:after {
      display: none
    }

    .van-divider--vertical.van-divider--hairline:before {
      transform: scaleX(.5)
    }

    :root {
      --van-dropdown-menu-height: 48px;
      --van-dropdown-menu-background: var(--van-background-2);
      --van-dropdown-menu-shadow: 0 2px 12px rgba(100, 101, 102, .12);
      --van-dropdown-menu-title-font-size: 15px;
      --van-dropdown-menu-title-text-color: var(--van-text-color);
      --van-dropdown-menu-title-active-text-color: var(--van-primary-color);
      --van-dropdown-menu-title-disabled-text-color: var(--van-text-color-2);
      --van-dropdown-menu-title-padding: 0 var(--van-padding-xs);
      --van-dropdown-menu-title-line-height: var(--van-line-height-lg);
      --van-dropdown-menu-option-active-color: var(--van-primary-color);
      --van-dropdown-menu-content-max-height: 80%
    }

    .van-dropdown-menu {
      -webkit-user-select: none;
      user-select: none
    }

    .van-dropdown-menu__bar {
      position: relative;
      display: flex;
      height: var(--van-dropdown-menu-height);
      background: var(--van-dropdown-menu-background);
      box-shadow: var(--van-dropdown-menu-shadow)
    }

    .van-dropdown-menu__bar--opened {
      z-index: calc(var(--van-dropdown-item-z-index) + 1)
    }

    .van-dropdown-menu__item {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      min-width: 0
    }

    .van-dropdown-menu__item--disabled .van-dropdown-menu__title {
      color: var(--van-dropdown-menu-title-disabled-text-color)
    }

    .van-dropdown-menu__title {
      position: relative;
      box-sizing: border-box;
      max-width: 100%;
      padding: var(--van-dropdown-menu-title-padding);
      color: var(--van-dropdown-menu-title-text-color);
      font-size: var(--van-dropdown-menu-title-font-size);
      line-height: var(--van-dropdown-menu-title-line-height)
    }

    .van-dropdown-menu__title:after {
      position: absolute;
      top: 50%;
      right: -4px;
      margin-top: -5px;
      border: 3px solid;
      border-color: transparent transparent var(--van-gray-4) var(--van-gray-4);
      transform: rotate(-45deg);
      opacity: .8;
      content: ""
    }

    .van-dropdown-menu__title--active {
      color: var(--van-dropdown-menu-title-active-text-color)
    }

    .van-dropdown-menu__title--active:after {
      border-color: transparent transparent currentColor currentColor
    }

    .van-dropdown-menu__title--down:after {
      margin-top: -1px;
      transform: rotate(135deg)
    }

    :root {
      --van-dropdown-item-z-index: 10
    }

    .van-dropdown-item {
      position: fixed;
      right: 0;
      left: 0;
      z-index: var(--van-dropdown-item-z-index);
      overflow: hidden
    }

    .van-dropdown-item__icon {
      display: block;
      line-height: inherit
    }

    .van-dropdown-item__option {
      text-align: left
    }

    .van-dropdown-item__option--active,
    .van-dropdown-item__option--active .van-dropdown-item__icon {
      color: var(--van-dropdown-menu-option-active-color)
    }

    .van-dropdown-item--up {
      top: 0
    }

    .van-dropdown-item--down {
      bottom: 0
    }

    .van-dropdown-item__content {
      position: absolute;
      max-height: var(--van-dropdown-menu-content-max-height)
    }

    .van-grid {
      display: flex;
      flex-wrap: wrap
    }

    :root {
      --van-grid-item-content-padding: var(--van-padding-md) var(--van-padding-xs);
      --van-grid-item-content-background: var(--van-background-2);
      --van-grid-item-content-active-color: var(--van-active-color);
      --van-grid-item-icon-size: 28px;
      --van-grid-item-text-color: var(--van-text-color);
      --van-grid-item-text-font-size: var(--van-font-size-sm)
    }

    .van-grid-item {
      position: relative;
      box-sizing: border-box
    }

    .van-grid-item--square {
      height: 0
    }

    .van-grid-item__icon {
      font-size: var(--van-grid-item-icon-size)
    }

    .van-grid-item__text {
      color: var(--van-grid-item-text-color);
      font-size: var(--van-grid-item-text-font-size);
      line-height: 1.5;
      word-break: break-all
    }

    .van-grid-item__icon+.van-grid-item__text {
      margin-top: var(--van-padding-xs)
    }

    .van-grid-item__content {
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      height: 100%;
      padding: var(--van-grid-item-content-padding);
      background: var(--van-grid-item-content-background)
    }

    .van-grid-item__content:after {
      z-index: 1;
      border-width: 0 var(--van-border-width) var(--van-border-width) 0
    }

    .van-grid-item__content--square {
      position: absolute;
      top: 0;
      right: 0;
      left: 0
    }

    .van-grid-item__content--center {
      align-items: center;
      justify-content: center
    }

    .van-grid-item__content--horizontal {
      flex-direction: row
    }

    .van-grid-item__content--horizontal .van-grid-item__text {
      margin: 0 0 0 var(--van-padding-xs)
    }

    .van-grid-item__content--reverse {
      flex-direction: column-reverse
    }

    .van-grid-item__content--reverse .van-grid-item__text {
      margin: 0 0 var(--van-padding-xs)
    }

    .van-grid-item__content--horizontal.van-grid-item__content--reverse {
      flex-direction: row-reverse
    }

    .van-grid-item__content--horizontal.van-grid-item__content--reverse .van-grid-item__text {
      margin: 0 var(--van-padding-xs) 0 0
    }

    .van-grid-item__content--surround:after {
      border-width: var(--van-border-width)
    }

    .van-grid-item__content--clickable {
      cursor: pointer
    }

    .van-grid-item__content--clickable:active {
      background-color: var(--van-grid-item-content-active-color)
    }

    :root {
      --van-index-bar-sidebar-z-index: 2;
      --van-index-bar-index-font-size: var(--van-font-size-xs);
      --van-index-bar-index-line-height: var(--van-line-height-xs);
      --van-index-bar-index-active-color: var(--van-primary-color)
    }

    .van-index-bar__sidebar {
      position: fixed;
      top: 50%;
      right: 0;
      z-index: var(--van-index-bar-sidebar-z-index);
      display: flex;
      flex-direction: column;
      text-align: center;
      transform: translateY(-50%);
      cursor: pointer;
      -webkit-user-select: none;
      user-select: none
    }

    .van-index-bar__index {
      padding: 0 var(--van-padding-xs) 0 var(--van-padding-md);
      font-weight: var(--van-font-bold);
      font-size: var(--van-index-bar-index-font-size);
      line-height: var(--van-index-bar-index-line-height)
    }

    .van-index-bar__index--active {
      color: var(--van-index-bar-index-active-color);
      font-weight: 700
    }

    :root {
      --van-index-anchor-z-index: 1;
      --van-index-anchor-padding: 0 var(--van-padding-md);
      --van-index-anchor-text-color: var(--van-text-color);
      --van-index-anchor-font-weight: var(--van-font-bold);
      --van-index-anchor-font-size: var(--van-font-size-md);
      --van-index-anchor-line-height: 32px;
      --van-index-anchor-background: transparent;
      --van-index-anchor-sticky-text-color: var(--van-primary-color);
      --van-index-anchor-sticky-background: var(--van-background-2)
    }

    .van-index-anchor {
      z-index: var(--van-index-anchor-z-index);
      box-sizing: border-box;
      padding: var(--van-index-anchor-padding);
      color: var(--van-index-anchor-text-color);
      font-weight: var(--van-index-anchor-font-weight);
      font-size: var(--van-index-anchor-font-size);
      line-height: var(--van-index-anchor-line-height);
      background: var(--van-index-anchor-background)
    }

    .van-index-anchor--sticky {
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      color: var(--van-index-anchor-sticky-text-color);
      background: var(--van-index-anchor-sticky-background)
    }

    :root {
      --van-pagination-height: 40px;
      --van-pagination-font-size: var(--van-font-size-md);
      --van-pagination-item-width: 36px;
      --van-pagination-item-default-color: var(--van-primary-color);
      --van-pagination-item-disabled-color: var(--van-gray-7);
      --van-pagination-item-disabled-background: var(--van-background);
      --van-pagination-background: var(--van-background-2);
      --van-pagination-desc-color: var(--van-gray-7);
      --van-pagination-disabled-opacity: var(--van-disabled-opacity)
    }

    .van-pagination {
      font-size: var(--van-pagination-font-size)
    }

    .van-pagination__items {
      display: flex
    }

    .van-pagination__item,
    .van-pagination__page-desc {
      display: flex;
      align-items: center;
      justify-content: center
    }

    .van-pagination__item {
      flex: 1;
      box-sizing: border-box;
      min-width: var(--van-pagination-item-width);
      height: var(--van-pagination-height);
      color: var(--van-pagination-item-default-color);
      background: var(--van-pagination-background);
      cursor: pointer;
      -webkit-user-select: none;
      user-select: none
    }

    .van-pagination__item uni-button {
      flex: 1;
      height: 100%;
      border: none;
      padding: 0;
      background: transparent
    }

    .van-pagination__item uni-button[disabled] {
      cursor: not-allowed
    }

    .van-pagination__item:active {
      color: var(--van-white);
      background-color: var(--van-pagination-item-default-color)
    }

    .van-pagination__item:not(:last-child):after {
      border-right-width: 0
    }

    .van-pagination__item--active {
      color: var(--van-white);
      background-color: var(--van-pagination-item-default-color)
    }

    .van-pagination__item--page {
      flex-grow: 0
    }

    .van-pagination__item--prev,
    .van-pagination__item--next {
      padding: 0 var(--van-padding-base);
      cursor: pointer
    }

    .van-pagination__item--border:after {
      border-width: var(--van-border-width)
    }

    .van-pagination__item--disabled,
    .van-pagination__item--disabled:active {
      color: var(--van-pagination-item-disabled-color);
      background-color: var(--van-pagination-item-disabled-background);
      opacity: var(--van-pagination-disabled-opacity)
    }

    .van-pagination__page-desc {
      flex: 1;
      height: var(--van-pagination-height);
      color: var(--van-pagination-desc-color)
    }

    :root {
      --van-password-input-height: 50px;
      --van-password-input-margin: 0 var(--van-padding-md);
      --van-password-input-font-size: 20px;
      --van-password-input-radius: 6px;
      --van-password-input-background: var(--van-background-2);
      --van-password-input-info-color: var(--van-text-color-2);
      --van-password-input-info-font-size: var(--van-font-size-md);
      --van-password-input-error-info-color: var(--van-danger-color);
      --van-password-input-dot-size: 10px;
      --van-password-input-dot-color: var(--van-text-color);
      --van-password-input-text-color: var(--van-text-color);
      --van-password-input-cursor-color: var(--van-text-color);
      --van-password-input-cursor-width: 1px;
      --van-password-input-cursor-height: 40%;
      --van-password-input-cursor-duration: 1s
    }

    .van-password-input {
      position: relative;
      margin: var(--van-password-input-margin);
      -webkit-user-select: none;
      user-select: none
    }

    .van-password-input__info,
    .van-password-input__error-info {
      margin-top: var(--van-padding-md);
      font-size: var(--van-password-input-info-font-size);
      text-align: center
    }

    .van-password-input__info {
      color: var(--van-password-input-info-color)
    }

    .van-password-input__error-info {
      color: var(--van-password-input-error-info-color)
    }

    .van-password-input__security {
      display: flex;
      width: 100%;
      height: var(--van-password-input-height);
      cursor: pointer
    }

    .van-password-input__security:after {
      border-radius: var(--van-password-input-radius)
    }

    .van-password-input__security li {
      position: relative;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: var(--van-password-input-text-color);
      font-size: var(--van-password-input-font-size);
      line-height: 1.2;
      background: var(--van-password-input-background)
    }

    .van-password-input__security i {
      position: absolute;
      top: 50%;
      left: 50%;
      width: var(--van-password-input-dot-size);
      height: var(--van-password-input-dot-size);
      background: var(--van-password-input-dot-color);
      border-radius: 100%;
      transform: translate(-50%, -50%);
      visibility: hidden
    }

    .van-password-input__cursor {
      position: absolute;
      top: 50%;
      left: 50%;
      width: var(--van-password-input-cursor-width);
      height: var(--van-password-input-cursor-height);
      background: var(--van-password-input-cursor-color);
      transform: translate(-50%, -50%);
      animation: var(--van-password-input-cursor-duration) van-cursor-flicker infinite
    }

    @keyframes van-cursor-flicker {
      0% {
        opacity: 0
      }

      50% {
        opacity: 1
      }

      to {
        opacity: 0
      }
    }

    :root {
      --van-progress-height: 4px;
      --van-progress-color: var(--van-primary-color);
      --van-progress-inactive-color: var(--van-gray-5);
      --van-progress-background: var(--van-gray-3);
      --van-progress-pivot-padding: 0 5px;
      --van-progress-pivot-text-color: var(--van-white);
      --van-progress-pivot-font-size: var(--van-font-size-xs);
      --van-progress-pivot-line-height: 1.6;
      --van-progress-pivot-background: var(--van-primary-color)
    }

    .van-progress {
      position: relative;
      height: var(--van-progress-height);
      background: var(--van-progress-background);
      border-radius: var(--van-progress-height)
    }

    .van-progress__portion {
      position: absolute;
      left: 0;
      width: 100%;
      height: 100%;
      background: var(--van-progress-color);
      border-radius: inherit;
      transform-origin: 0;
      transition: all var(--van-duration-base) var(--van-ease-out)
    }

    .van-progress__portion--inactive {
      background: var(--van-progress-inactive-color)
    }

    .van-progress__pivot {
      position: absolute;
      top: 50%;
      box-sizing: border-box;
      min-width: 3.6em;
      padding: var(--van-progress-pivot-padding);
      color: var(--van-progress-pivot-text-color);
      font-size: var(--van-progress-pivot-font-size);
      line-height: var(--van-progress-pivot-line-height);
      text-align: center;
      word-break: keep-all;
      background: var(--van-progress-pivot-background);
      border-radius: 1em;
      transition: all var(--van-duration-base) var(--van-ease-out)
    }

    .van-progress__pivot--inactive {
      background: var(--van-progress-inactive-color)
    }

    :root {
      --van-sidebar-width: 80px
    }

    .van-sidebar {
      width: var(--van-sidebar-width);
      overflow-y: auto;
      -webkit-overflow-scrolling: touch
    }

    :root {
      --van-sidebar-font-size: var(--van-font-size-md);
      --van-sidebar-line-height: var(--van-line-height-md);
      --van-sidebar-text-color: var(--van-text-color);
      --van-sidebar-disabled-text-color: var(--van-text-color-3);
      --van-sidebar-padding: 20px var(--van-padding-sm);
      --van-sidebar-active-color: var(--van-active-color);
      --van-sidebar-background: var(--van-background);
      --van-sidebar-selected-font-weight: var(--van-font-bold);
      --van-sidebar-selected-text-color: var(--van-text-color);
      --van-sidebar-selected-border-width: 4px;
      --van-sidebar-selected-border-height: 16px;
      --van-sidebar-selected-border-color: var(--van-primary-color);
      --van-sidebar-selected-background: var(--van-background-2)
    }

    .van-sidebar-item {
      position: relative;
      display: block;
      box-sizing: border-box;
      padding: var(--van-sidebar-padding);
      overflow: hidden;
      color: var(--van-sidebar-text-color);
      font-size: var(--van-sidebar-font-size);
      line-height: var(--van-sidebar-line-height);
      background: var(--van-sidebar-background);
      cursor: pointer;
      -webkit-user-select: none;
      user-select: none
    }

    .van-sidebar-item:active {
      background-color: var(--van-sidebar-active-color)
    }

    .van-sidebar-item:not(:last-child):after {
      border-bottom-width: 1px
    }

    .van-sidebar-item__text {
      word-break: break-all
    }

    .van-sidebar-item--select {
      color: var(--van-sidebar-selected-text-color);
      font-weight: var(--van-sidebar-selected-font-weight)
    }

    .van-sidebar-item--select,
    .van-sidebar-item--select:active {
      background-color: var(--van-sidebar-selected-background)
    }

    .van-sidebar-item--select:before {
      position: absolute;
      top: 50%;
      left: 0;
      width: var(--van-sidebar-selected-border-width);
      height: var(--van-sidebar-selected-border-height);
      background-color: var(--van-sidebar-selected-border-color);
      transform: translateY(-50%);
      content: ""
    }

    .van-sidebar-item--disabled {
      color: var(--van-sidebar-disabled-text-color);
      cursor: not-allowed
    }

    .van-sidebar-item--disabled:active {
      background-color: var(--van-sidebar-background)
    }

    :root {
      --van-tree-select-font-size: var(--van-font-size-md);
      --van-tree-select-nav-background: var(--van-background);
      --van-tree-select-content-background: var(--van-background-2);
      --van-tree-select-nav-item-padding: 14px var(--van-padding-sm);
      --van-tree-select-item-height: 48px;
      --van-tree-select-item-active-color: var(--van-primary-color);
      --van-tree-select-item-disabled-color: var(--van-gray-5);
      --van-tree-select-item-selected-size: 16px
    }

    .van-tree-select {
      position: relative;
      display: flex;
      font-size: var(--van-tree-select-font-size)
    }

    .van-tree-select__nav {
      flex: 1;
      overflow-y: auto;
      background: var(--van-tree-select-nav-background);
      -webkit-overflow-scrolling: touch
    }

    .van-tree-select__nav-item {
      padding: var(--van-tree-select-nav-item-padding)
    }

    .van-tree-select__content {
      flex: 2;
      overflow-y: auto;
      background: var(--van-tree-select-content-background);
      -webkit-overflow-scrolling: touch
    }

    .van-tree-select__item {
      position: relative;
      padding: 0 32px 0 var(--van-padding-md);
      font-weight: var(--van-font-bold);
      line-height: var(--van-tree-select-item-height);
      -webkit-user-select: none;
      user-select: none;
      cursor: pointer
    }

    .van-tree-select__item--active {
      color: var(--van-tree-select-item-active-color)
    }

    .van-tree-select__item:active {
      background-color: var(--van-active-color)
    }

    .van-tree-select__item--disabled {
      color: var(--van-tree-select-item-disabled-color);
      cursor: not-allowed
    }

    .van-tree-select__item--disabled:active {
      background-color: transparent
    }

    .van-tree-select__selected {
      position: absolute;
      top: 50%;
      right: var(--van-padding-md);
      margin-top: calc(var(--van-padding-xs) * -1);
      font-size: var(--van-tree-select-item-selected-size)
    }

    :root {
      --van-skeleton-title-width: 40%
    }

    .van-skeleton-title {
      height: var(--van-skeleton-paragraph-height);
      background: var(--van-skeleton-paragraph-background)
    }

    .van-skeleton-title--round {
      border-radius: var(--van-radius-max)
    }

    .van-skeleton-title {
      width: var(--van-skeleton-title-width);
      margin: 0
    }

    .van-skeleton-title+.van-skeleton-paragraph {
      margin-top: 20px
    }

    :root {
      --van-skeleton-avatar-size: 32px;
      --van-skeleton-avatar-background: var(--van-active-color)
    }

    .van-skeleton-avatar {
      flex-shrink: 0;
      width: var(--van-skeleton-avatar-size);
      height: var(--van-skeleton-avatar-size);
      margin-right: var(--van-padding-md);
      background: var(--van-skeleton-avatar-background)
    }

    .van-skeleton-avatar--round {
      border-radius: var(--van-radius-max)
    }

    .van-skeleton-avatar+.van-skeleton__content {
      padding-top: var(--van-padding-xs)
    }

    :root {
      --van-skeleton-paragraph-height: 16px;
      --van-skeleton-paragraph-background: var(--van-active-color);
      --van-skeleton-paragraph-margin-top: var(--van-padding-sm)
    }

    .van-skeleton-paragraph {
      height: var(--van-skeleton-paragraph-height);
      background: var(--van-skeleton-paragraph-background)
    }

    .van-skeleton-paragraph--round {
      border-radius: var(--van-radius-max)
    }

    .van-skeleton-paragraph:not(:first-child) {
      margin-top: var(--van-skeleton-paragraph-margin-top)
    }

    :root {
      --van-skeleton-duration: 1.2s
    }

    .van-skeleton {
      display: flex;
      padding: 0 var(--van-padding-md)
    }

    .van-skeleton__content {
      width: 100%
    }

    .van-skeleton--animate {
      animation: van-skeleton-blink var(--van-skeleton-duration) ease-in-out infinite
    }

    @keyframes van-skeleton-blink {
      50% {
        opacity: .6
      }
    }

    :root {
      --van-slider-active-background: var(--van-primary-color);
      --van-slider-inactive-background: var(--van-gray-3);
      --van-slider-disabled-opacity: var(--van-disabled-opacity);
      --van-slider-bar-height: 2px;
      --van-slider-button-width: 24px;
      --van-slider-button-height: 24px;
      --van-slider-button-radius: 50%;
      --van-slider-button-background: var(--van-white);
      --van-slider-button-shadow: 0 1px 2px rgba(0, 0, 0, .5)
    }

    .van-theme-dark {
      --van-slider-inactive-background: var(--van-background-3)
    }

    .van-slider {
      position: relative;
      width: 100%;
      height: var(--van-slider-bar-height);
      background: var(--van-slider-inactive-background);
      border-radius: var(--van-radius-max);
      cursor: pointer
    }

    .van-slider:before {
      position: absolute;
      top: calc(var(--van-padding-xs) * -1);
      right: 0;
      bottom: calc(var(--van-padding-xs) * -1);
      left: 0;
      content: ""
    }

    .van-slider__bar {
      position: absolute;
      width: 100%;
      height: 100%;
      background: var(--van-slider-active-background);
      border-radius: inherit;
      transition: all var(--van-duration-fast)
    }

    .van-slider__button {
      width: var(--van-slider-button-width);
      height: var(--van-slider-button-height);
      background: var(--van-slider-button-background);
      border-radius: var(--van-slider-button-radius);
      box-shadow: var(--van-slider-button-shadow)
    }

    .van-slider__button-wrapper {
      position: absolute;
      cursor: grab;
      top: 50%
    }

    .van-slider__button-wrapper--right {
      right: 0;
      transform: translate3d(50%, -50%, 0)
    }

    .van-slider__button-wrapper--left {
      left: 0;
      transform: translate3d(-50%, -50%, 0)
    }

    .van-slider--disabled {
      cursor: not-allowed;
      opacity: var(--van-slider-disabled-opacity)
    }

    .van-slider--disabled .van-slider__button-wrapper {
      cursor: not-allowed
    }

    .van-slider--vertical {
      display: inline-block;
      width: var(--van-slider-bar-height);
      height: 100%
    }

    .van-slider--vertical .van-slider__button-wrapper--right {
      top: auto;
      right: 50%;
      bottom: 0;
      transform: translate3d(50%, 50%, 0)
    }

    .van-slider--vertical .van-slider__button-wrapper--left {
      top: 0;
      right: 50%;
      left: auto;
      transform: translate3d(50%, -50%, 0)
    }

    .van-slider--vertical:before {
      top: 0;
      right: calc(var(--van-padding-xs) * -1);
      bottom: 0;
      left: calc(var(--van-padding-xs) * -1)
    }

    .van-space {
      display: inline-flex
    }

    .van-space--horizontal .van-space-item {
      display: flex;
      align-items: center
    }

    .van-space--vertical {
      flex-direction: column
    }

    .van-space--align-baseline {
      align-items: baseline
    }

    .van-space--align-start {
      align-items: flex-start
    }

    .van-space--align-end {
      align-items: flex-end
    }

    .van-space--align-center {
      align-items: center
    }

    .van-space--wrap {
      flex-wrap: wrap
    }

    .van-space--fill {
      display: flex
    }

    :root {
      --van-steps-background: var(--van-background-2)
    }

    .van-steps {
      overflow: hidden;
      background-color: var(--van-steps-background)
    }

    .van-steps--horizontal {
      padding: 10px 10px 0
    }

    .van-steps--horizontal .van-steps__items {
      position: relative;
      display: flex;
      margin: 0 0 10px;
      padding-bottom: 22px
    }

    .van-steps--vertical {
      padding: 0 0 0 var(--van-padding-xl)
    }

    :root {
      --van-step-text-color: var(--van-text-color-2);
      --van-step-active-color: var(--van-primary-color);
      --van-step-process-text-color: var(--van-text-color);
      --van-step-font-size: var(--van-font-size-md);
      --van-step-line-color: var(--van-border-color);
      --van-step-finish-line-color: var(--van-primary-color);
      --van-step-finish-text-color: var(--van-text-color);
      --van-step-icon-size: 12px;
      --van-step-circle-size: 5px;
      --van-step-circle-color: var(--van-gray-6);
      --van-step-horizontal-title-font-size: var(--van-font-size-sm)
    }

    .van-step {
      position: relative;
      flex: 1;
      color: var(--van-step-text-color);
      font-size: var(--van-step-font-size)
    }

    .van-step__circle {
      display: block;
      width: var(--van-step-circle-size);
      height: var(--van-step-circle-size);
      background-color: var(--van-step-circle-color);
      border-radius: 50%
    }

    .van-step__line {
      position: absolute;
      background-color: var(--van-step-line-color);
      transition: background-color var(--van-duration-base)
    }

    .van-step--horizontal {
      float: left
    }

    .van-step--horizontal:first-child .van-step__title {
      margin-left: 0;
      transform: none
    }

    .van-step--horizontal:last-child:not(:first-child) {
      position: absolute;
      right: 1px;
      width: auto
    }

    .van-step--horizontal:last-child:not(:first-child) .van-step__title {
      margin-left: 0;
      transform: none
    }

    .van-step--horizontal:last-child:not(:first-child) .van-step__circle-container {
      right: -9px;
      left: auto
    }

    .van-step--horizontal .van-step__circle-container {
      position: absolute;
      top: 30px;
      left: calc(var(--van-padding-xs) * -1);
      z-index: 1;
      padding: 0 var(--van-padding-xs);
      background-color: var(--van-background-2);
      transform: translateY(-50%)
    }

    .van-step--horizontal .van-step__title {
      display: inline-block;
      margin-left: 3px;
      font-size: var(--van-step-horizontal-title-font-size);
      transform: translate(-50%)
    }

    .van-step--horizontal .van-step__line {
      top: 30px;
      left: 0;
      width: 100%;
      height: 1px
    }

    .van-step--horizontal .van-step__icon {
      display: block;
      font-size: var(--van-step-icon-size)
    }

    .van-step--horizontal .van-step--process {
      color: var(--van-step-process-text-color)
    }

    .van-step--vertical {
      display: block;
      float: none;
      padding: 10px 10px 10px 0;
      line-height: var(--van-line-height-sm)
    }

    .van-step--vertical:not(:last-child):after {
      border-bottom-width: 1px
    }

    .van-step--vertical .van-step__circle-container {
      position: absolute;
      top: 19px;
      left: -15px;
      z-index: 1;
      font-size: var(--van-step-icon-size);
      line-height: 1;
      transform: translate(-50%, -50%)
    }

    .van-step--vertical .van-step__line {
      top: 16px;
      left: -15px;
      width: 1px;
      height: 100%
    }

    .van-step:last-child .van-step__line {
      width: 0
    }

    .van-step--finish {
      color: var(--van-step-finish-text-color)
    }

    .van-step--finish .van-step__circle,
    .van-step--finish .van-step__line {
      background-color: var(--van-step-finish-line-color)
    }

    .van-step__icon,
    .van-step__title {
      transition: color var(--van-duration-base)
    }

    .van-step__icon--active,
    .van-step__title--active,
    .van-step__icon--finish,
    .van-step__title--finish {
      color: var(--van-step-active-color)
    }

    :root {
      --van-stepper-background: var(--van-active-color);
      --van-stepper-button-icon-color: var(--van-text-color);
      --van-stepper-button-disabled-color: var(--van-background);
      --van-stepper-button-disabled-icon-color: var(--van-gray-5);
      --van-stepper-button-round-theme-color: var(--van-primary-color);
      --van-stepper-input-width: 32px;
      --van-stepper-input-height: 28px;
      --van-stepper-input-font-size: var(--van-font-size-md);
      --van-stepper-input-line-height: normal;
      --van-stepper-input-text-color: var(--van-text-color);
      --van-stepper-input-disabled-text-color: var(--van-text-color-3);
      --van-stepper-input-disabled-background: var(--van-active-color);
      --van-stepper-radius: var(--van-radius-md)
    }

    .van-stepper {
      display: inline-block;
      -webkit-user-select: none;
      user-select: none
    }

    .van-stepper__minus,
    .van-stepper__plus {
      position: relative;
      box-sizing: border-box;
      width: var(--van-stepper-input-height);
      height: var(--van-stepper-input-height);
      margin: 0;
      padding: 0;
      color: var(--van-stepper-button-icon-color);
      vertical-align: middle;
      background: var(--van-stepper-background);
      border: 0
    }

    .van-stepper__minus:before,
    .van-stepper__plus:before {
      width: 50%;
      height: 1px
    }

    .van-stepper__minus:after,
    .van-stepper__plus:after {
      width: 1px;
      height: 50%
    }

    .van-stepper__minus:before,
    .van-stepper__plus:before,
    .van-stepper__minus:after,
    .van-stepper__plus:after {
      position: absolute;
      top: 50%;
      left: 50%;
      background-color: currentColor;
      transform: translate(-50%, -50%);
      content: ""
    }

    .van-stepper__minus--disabled,
    .van-stepper__plus--disabled {
      color: var(--van-stepper-button-disabled-icon-color);
      background-color: var(--van-stepper-button-disabled-color);
      cursor: not-allowed
    }

    .van-stepper__minus {
      border-radius: var(--van-stepper-radius) 0 0 var(--van-stepper-radius)
    }

    .van-stepper__minus:after {
      display: none
    }

    .van-stepper__plus {
      border-radius: 0 var(--van-stepper-radius) var(--van-stepper-radius) 0
    }

    .van-stepper__input {
      box-sizing: border-box;
      width: var(--van-stepper-input-width);
      height: var(--van-stepper-input-height);
      margin: 0 2px;
      padding: 0;
      color: var(--van-stepper-input-text-color);
      font-size: var(--van-stepper-input-font-size);
      line-height: var(--van-stepper-input-line-height);
      text-align: center;
      vertical-align: middle;
      background: var(--van-stepper-background);
      border: 0;
      border-width: 1px 0;
      border-radius: 0;
      -webkit-appearance: none
    }

    .van-stepper__input:disabled {
      color: var(--van-stepper-input-disabled-text-color);
      background-color: var(--van-stepper-input-disabled-background);
      -webkit-text-fill-color: var(--van-stepper-input-disabled-text-color);
      opacity: 1
    }

    .van-stepper__input:read-only {
      cursor: default
    }

    .van-stepper--round .van-stepper__input {
      background-color: transparent
    }

    .van-stepper--round .van-stepper__plus,
    .van-stepper--round .van-stepper__minus {
      border-radius: 100%
    }

    .van-stepper--round .van-stepper__plus--disabled,
    .van-stepper--round .van-stepper__minus--disabled {
      opacity: .3;
      cursor: not-allowed
    }

    .van-stepper--round .van-stepper__plus {
      color: var(--van-white);
      background: var(--van-stepper-button-round-theme-color)
    }

    .van-stepper--round .van-stepper__minus {
      color: var(--van-stepper-button-round-theme-color);
      background-color: var(--van-background-2);
      border: 1px solid var(--van-stepper-button-round-theme-color)
    }

    .van-swipe-cell {
      position: relative;
      overflow: hidden;
      cursor: grab
    }

    .van-swipe-cell__wrapper {
      transition-timing-function: cubic-bezier(.18, .89, .32, 1);
      transition-property: transform
    }

    .van-swipe-cell__left,
    .van-swipe-cell__right {
      position: absolute;
      top: 0;
      height: 100%
    }

    .van-swipe-cell__left {
      left: 0;
      transform: translate3d(-100%, 0, 0)
    }

    .van-swipe-cell__right {
      right: 0;
      transform: translate3d(100%, 0, 0)
    }

    :root {
      --van-tabbar-height: 50px;
      --van-tabbar-z-index: 1;
      --van-tabbar-background: var(--van-background-2)
    }

    .van-tabbar {
      z-index: var(--van-tabbar-z-index);
      display: flex;
      box-sizing: content-box;
      width: 100%;
      height: var(--van-tabbar-height);
      background: var(--van-tabbar-background)
    }

    .van-tabbar--fixed {
      position: fixed;
      bottom: 0;
      left: 0
    }

    :root {
      --van-tabbar-item-font-size: var(--van-font-size-sm);
      --van-tabbar-item-text-color: var(--van-text-color);
      --van-tabbar-item-active-color: var(--van-primary-color);
      --van-tabbar-item-active-background: var(--van-background-2);
      --van-tabbar-item-line-height: 1;
      --van-tabbar-item-icon-size: 22px;
      --van-tabbar-item-icon-margin-bottom: var(--van-padding-base)
    }

    .van-tabbar-item {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--van-tabbar-item-text-color);
      font-size: var(--van-tabbar-item-font-size);
      line-height: var(--van-tabbar-item-line-height);
      cursor: pointer
    }

    .van-tabbar-item__icon {
      margin-bottom: var(--van-tabbar-item-icon-margin-bottom);
      font-size: var(--van-tabbar-item-icon-size)
    }

    .van-tabbar-item__icon .van-icon {
      display: block
    }

    .van-tabbar-item__icon .van-badge {
      margin-top: var(--van-padding-base)
    }

    .van-tabbar-item__icon img {
      display: block;
      height: 20px
    }

    .van-tabbar-item--active {
      color: var(--van-tabbar-item-active-color);
      background-color: var(--van-tabbar-item-active-background)
    }

    :root {
      --van-text-ellipsis-line-height: 1.6;
      --van-text-ellipsis-action-color: var(--van-blue)
    }

    .van-text-ellipsis {
      line-height: var(--van-text-ellipsis-line-height);
      white-space: pre-wrap;
      word-break: break-word
    }

    .van-text-ellipsis__action {
      cursor: pointer;
      color: var(--van-text-ellipsis-action-color)
    }

    .van-text-ellipsis__action:active {
      opacity: var(--van-active-opacity)
    }

    :root {
      --van-watermark-z-index: 100
    }

    .van-watermark {
      position: absolute;
      height: 100%;
      width: 100%;
      left: 0;
      top: 0;
      z-index: var(--van-watermark-z-index);
      background-repeat: repeat;
      pointer-events: none
    }

    .van-watermark__wrapper {
      display: none
    }

    .van-watermark--full {
      position: fixed
    }
  </style>
  <link rel="modulepreload" as="script" crossorigin="" data-savepage-href="/assets/pages-d4-Home-index.VPlrpdvC.js"
    href="">
  <link rel="modulepreload" as="script" crossorigin="" data-savepage-href="/assets/page-meta.AYkUiJi8.js" href="">
  <link rel="modulepreload" as="script" crossorigin="" data-savepage-href="/assets/image-card.BbEzzhP6.js" href="">
  <link rel="modulepreload" as="script" crossorigin="" data-savepage-href="/assets/card.CWrEoS1m.js" href="">
  <style data-savepage-href="/assets/card-DKlKI4W8.css">
    .card[data-v-f07d8703] {
      margin-bottom: .75rem;
      overflow: hidden;
      position: relative
    }

    .card.clickable[data-v-f07d8703] {
      cursor: pointer
    }

    .card.clickable[data-v-f07d8703]:hover {
      border-color: var(--color-primary)
    }

    .card .title[data-v-f07d8703] {
      color: var(--color-primary);
      font-size: 1rem;
      padding: .75rem
    }

    .card .body[data-v-f07d8703] {
      padding: .75rem;
      color: var(--color-text-normal)
    }

    .card .body.bodyCenter[data-v-f07d8703] {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column
    }

    .card .title+.body[data-v-f07d8703] {
      padding-top: 0
    }

    .game-tli .card[data-v-f07d8703] {
      background-color: #000;
      border-color: #323232
    }
  </style>
  <link rel="modulepreload" as="script" crossorigin="" data-savepage-href="/assets/log.D5Ozcvy9.js" href="">
  <style data-savepage-href="/assets/image-card-q-qWnRrL.css">
    .uni-swiper__warp[data-v-77e8f2a6] {
      display: flex;
      flex: 1;
      flex-direction: column;
      position: relative;
      overflow: hidden
    }

    .uni-swiper__dots-box[data-v-77e8f2a6] {
      position: absolute;
      bottom: 10px;
      left: 0;
      right: 0;
      display: flex;
      flex: 1;
      flex-direction: row;
      justify-content: center;
      align-items: center
    }

    .uni-swiper__dots-item[data-v-77e8f2a6] {
      width: 8px;
      border-radius: 100px;
      margin-left: 6px;
      background-color: rgba(0, 0, 0, .4);
      cursor: pointer
    }

    .uni-swiper__dots-item[data-v-77e8f2a6]:first-child {
      margin: 0
    }

    .uni-swiper__dots-default[data-v-77e8f2a6] {
      border-radius: 100px
    }

    .uni-swiper__dots-long[data-v-77e8f2a6],
    .uni-swiper__dots-bar[data-v-77e8f2a6] {
      border-radius: 50px
    }

    .uni-swiper__dots-nav[data-v-77e8f2a6] {
      bottom: 0;
      padding: 8px 0;
      display: flex;
      flex: 1;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      background-color: rgba(0, 0, 0, .2)
    }

    .uni-swiper__dots-nav-item[data-v-77e8f2a6] {
      font-size: 14px;
      color: #fff;
      margin: 0 15px
    }

    .uni-swiper__dots-indexes[data-v-77e8f2a6] {
      display: flex;
      justify-content: center;
      align-items: center
    }

    .uni-swiper__dots-indexes-text[data-v-77e8f2a6] {
      color: #fff;
      font-size: 12px;
      line-height: 14px
    }

    .banner[data-v-b660c215] {
      height: 1.8125rem;
      box-shadow: 0 0 1px rgba(0, 0, 0, .51);
      background-color: var(--color-bg)
    }

    .swiper[data-v-b660c215] {
      height: 7.8125rem
    }

    .img[data-v-b660c215] {
      width: 100%;
      height: 7.8125rem
    }

    .link[data-v-b660c215] {
      display: block
    }

    .drop[data-v-b660c215] {
      position: absolute;
      left: 0;
      bottom: 0;
      right: 0;
      background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, .8) 100%);
      padding: 1.5rem .75rem
    }

    .bottom[data-v-b660c215] {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: .375rem;
      line-height: 1;
      display: none
    }

    .author[data-v-b660c215] {
      color: var(--d4-color-unique)
    }

    .date[data-v-b660c215] {
      color: var(--color-text-grey);
      margin-left: .375rem
    }

    .banner-title[data-v-b660c215] {
      font-size: var(--font-size-xl);
      color: #fff;
      text-shadow: 1px 1px 4px rgba(0, 0, 0, .75);
      text-align: center
    }

    @media (min-width: 1024px) {

      .banner[data-v-b660c215],
      .swiper[data-v-b660c215],
      .img[data-v-b660c215] {
        height: 30px
      }

      .drop[data-v-b660c215] {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        padding-top: 1.5rem
      }

      .bottom[data-v-b660c215] {
        display: flex
      }

      .banner-title[data-v-b660c215] {
        bottom: 1.5rem;
        text-align: left
      }
    }

    uni-swiper-item[data-v-b660c215] {
      cursor: pointer
    }

    .item-grid[data-v-9036d778] {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      margin: -.375rem
    }

    .item-grid .item[data-v-9036d778] {
      background: radial-gradient(var(--color-bg), #000);
      flex: 1 0 20%;
      margin: .375rem;
      position: relative
    }

    .item-grid .item.disabled[data-v-9036d778] {
      filter: grayscale(100%)
    }

    .item-grid .item.disabled[data-v-9036d778] .footer {
      opacity: .4
    }

    .item-grid .item .footer[data-v-9036d778] {
      background-color: var(--color-bg);
      color: var(--color-text-normal);
      font-size: var(--font-size-normal);
      text-align: center;
      flex: 1;
      padding: .1875rem 0;
      white-space: nowrap
    }

    .item-grid .item .image-wrapper[data-v-9036d778] {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center
    }

    .item-grid .item .icon[data-v-9036d778] {
      position: relative;
      display: flex
    }

    .item-grid .item .item-img[data-v-9036d778] {
      margin-top: .46875rem;
      margin-bottom: .46875rem
    }

    .item-grid .item .badge[data-v-9036d778] {
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(50%, -50%);
      background-color: red;
      color: #fff;
      padding: 0 .125rem;
      font-size: .625rem
    }

    .item-grid .item .title[data-v-9036d778] {
      margin-top: .1875rem;
      font-size: .75rem;
      font-weight: 500;
      color: var(--color-text-normal);
      text-align: center;
      white-space: nowrap
    }

    @media (min-width: 1024px) {
      .item-grid .item[data-v-9036d778] {
        flex-basis: 10%
      }

      .item-grid .item .item-img[data-v-9036d778] {
        width: 90px !important;
        height: 90px !important;
        margin-top: 14px;
        margin-bottom: 14px
      }

      .game-tli .item-grid .item .item-img[data-v-9036d778] {
        width: 72px !important;
        height: 72px !important
      }
    }

    .page-footer[data-v-63d441c7] {
      color: var(--color-text-grey);
      padding: 1.5rem 0;
      background-color: rgba(0, 0, 0, .5)
    }

    .page-footer .footer-content[data-v-63d441c7] {
      display: flex;
      flex-direction: row;
      justify-content: center;
      color: var(--color-text-normal);
      margin-bottom: .375rem
    }

    .page-footer .footer-content .divider[data-v-63d441c7] {
      margin: 0 .75rem
    }

    .page-footer .copyright[data-v-63d441c7] {
      text-align: center
    }

    .page-footer .link[data-v-63d441c7] {
      cursor: pointer
    }

    .dialog-content[data-v-63d441c7] {
      padding: .75rem .75rem 1.5rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center
    }

    .qrcode-image[data-v-63d441c7] {
      width: 160px;
      height: 160px
    }

    @media (min-width: 1024px) {
      .page-footer[data-v-63d441c7] {
        background-color: transparent
      }
    }

    .dialog-content[data-v-b225a338] {
      padding: .75rem
    }

    .dialog-content .title[data-v-b225a338] {
      color: var(--color-primary);
      font-size: var(--font-size-large)
    }

    .dialog-content .date[data-v-b225a338] {
      margin-bottom: .375rem
    }

    .dialog-content ul[data-v-b225a338],
    .dialog-content ol[data-v-b225a338] {
      list-style: initial;
      padding-left: 1rem
    }

    .dialog-content .sub-title[data-v-b225a338] {
      color: var(--d4-color-unique);
      margin-bottom: .375rem;
      margin-top: .375rem
    }

    .dialog-content .text-normal[data-v-b225a338] {
      line-height: 1.5
    }

    .dialog-content .img[data-v-b225a338] {
      width: 100%;
      height: 200px;
      margin-bottom: .375rem
    }

    @media (min-width: 1024px) {
      .dialog-content .img[data-v-b225a338] {
        height: 500px
      }
    }

    .disabled[data-v-aa482c06] {
      filter: grayscale(100%)
    }

    .disabled .image-card[data-v-aa482c06] {
      opacity: .4
    }

    .image-card-wrapper[data-v-aa482c06] {
      flex: 1 1 40%;
      margin: .375rem
    }

    .image-card[data-v-aa482c06] {
      width: 100%;
      height: 3.75rem;
      color: var(--color-text-title);
      font-size: var(--font-size-normal);
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      padding: .375rem;
      background-size: cover;
      background-repeat: no-repeat;
      position: relative
    }

    .image-card .mask[data-v-aa482c06] {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, #000, rgba(0, 0, 0, .15) 50%)
    }

    .image-card .content[data-v-aa482c06] {
      z-index: 1
    }

    .game-tli .image-card-wrapper[data-v-aa482c06] {
      border: none;
      position: relative;
      overflow: visible;
      height: 7.5rem;
      background-color: transparent;
      box-shadow: none
    }

    .game-tli .image-card-wrapper[data-v-aa482c06]:before {
      display: block;
      content: " ";
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      background:
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/data_img/tli/ui/image-frame.png*/
        url(soccer.jpg) 0 0 no-repeat;
      background-size: 100% auto;
      z-index: 20;
      pointer-events: none
    }

    .game-tli .image-card[data-v-aa482c06] {
      height: 6.25rem
    }

    @media (min-width: 1024px) {
      .image-card-wrapper[data-v-aa482c06] {
        flex: 1
      }

      .image-card[data-v-aa482c06] {
        height: 100px;
        font-size: var(--font-size-large);
        justify-content: flex-start;
        padding: 12px
      }

      .game-tli .image-card-wrapper[data-v-aa482c06] {
        height: 156px
      }

      .game-tli .image-card[data-v-aa482c06] {
        height: 140px
      }
    }
  </style>
  <link rel="modulepreload" as="script" crossorigin="" data-savepage-href="/assets/notice.4CPsYIYO.js" href="">
  <style data-savepage-href="/assets/notice-d5Lfbyb8.css">
    .notice[data-v-eeb7f5ce] {
      cursor: pointer;
      margin-bottom: .75rem;
      background:
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/corner/tt_tl_tiny_i_m.webp*/
        var(--savepage-url-23) top left no-repeat,
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/corner/tt_tr_tiny_i_m.webp*/
        var(--savepage-url-24) top right no-repeat,
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/corner/tt_bl_tiny_i_m.webp*/
        var(--savepage-url-25) bottom left no-repeat,
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/corner/tt_br_tiny_i_m.webp*/
        var(--savepage-url-26) bottom right no-repeat,
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/corner/tt_border_t_tiny_i_b.webp*/
        var(--savepage-url-27) top repeat-x,
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/corner/tt_border_b_tiny_i_b.webp*/
        var(--savepage-url-28) bottom repeat-x,
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/corner/tt_border_l_tiny_i_b.webp*/
        var(--savepage-url-29) left repeat-y,
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/corner/tt_border_r_tiny_i_b.webp*/
        var(--savepage-url-30) right repeat-y,
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/corner/bgtile_bl.webp*/
        var(--savepage-url-31) repeat #000;
      box-shadow: 0 0 .25rem .125rem rgba(0, 0, 0, .2);
      text-align: center
    }

    .notice[data-v-eeb7f5ce] .van-notice-bar__wrap {
      justify-content: center
    }
  </style>
  <link rel="modulepreload" as="script" crossorigin="" data-savepage-href="/assets/section-title.PSOoVXIe.js" href="">
  <style data-savepage-href="/assets/section-title-DpiSXR-1.css">
    .section-title[data-v-6f33070d] {
      color: var(--color-primary);
      font-size: var(--font-size-large);
      margin-bottom: .75rem;
      margin-top: 0;
      text-shadow: 1px 1px 4px rgba(0, 0, 0, .75);
      display: flex;
      align-items: center;
      justify-content: space-between
    }

    .game-tli .section-title[data-v-6f33070d] {
      font-weight: 700;
      font-style: italic;
      font-size: 24px;
      color: #fff
    }

    @media (min-width: 1024px) {
      .section-title[data-v-6f33070d] {
        font-size: var(--font-size-large);
        line-height: 1.6
      }
    }
  </style>
  <link rel="modulepreload" as="script" crossorigin="" data-savepage-href="/assets/uni-countdown.D2csIDdZ.js" href="">
  <style data-savepage-href="/assets/uni-countdown-DINZNYO_.css">
    .uni-countdown[data-v-11f99eae] {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center
    }

    .uni-countdown__splitor[data-v-11f99eae] {
      margin: 0 2px;
      font-size: 14px;
      color: #333
    }

    .uni-countdown__number[data-v-11f99eae] {
      border-radius: 3px;
      text-align: center;
      font-size: 14px
    }
  </style>
  <style data-savepage-href="/assets/index-CZN3Ulei.css">
    .tools-wrapper[data-v-a597d84a] {
      margin-bottom: .75rem
    }

    .tools[data-v-a597d84a] {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      margin: -.375rem
    }

    .badge[data-v-a597d84a] {
      position: absolute;
      left: 3.4375rem;
      top: 1.875rem;
      background-color: red;
      color: #fff;
      padding: 0 .125rem;
      font-size: .625rem
    }

    .tag[data-v-a597d84a] {
      color: var(--color-red);
      border: 1px solid var(--color-red);
      display: inline-block;
      position: absolute;
      right: .375rem;
      top: .3125rem;
      background-color: var(--color-bg-dark);
      border-radius: .25rem;
      padding: .0625rem .1875rem
    }

    @media (min-width: 1024px) {
      .tag[data-v-a597d84a] {
        right: .5625rem;
        top: .5625rem;
        padding: .1875rem .375rem
      }
    }

    .guide-list[data-v-af6deea6] {
      display: flex;
      flex-direction: column
    }

    .guide-list .guide-item .inner[data-v-af6deea6] {
      display: flex
    }

    .guide-list .guide-item .item-img[data-v-af6deea6] {
      width: 6.75rem;
      height: 5.625rem;
      flex: 0 0 auto
    }

    .guide-list .guide-item .blz-icon[data-v-af6deea6] {
      width: 20px;
      height: 16px;
      vertical-align: middle;
      position: relative;
      top: -2px;
      margin-right: 6px
    }

    .guide-list .guide-item .content[data-v-af6deea6] {
      padding: .375rem .625rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between
    }

    .guide-list .guide-item .title[data-v-af6deea6] {
      color: var(--color-text-title);
      font-size: var(--font-size-normal);
      min-height: 2.375rem;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical
    }

    .guide-list .guide-item .author[data-v-af6deea6] {
      font-size: var(--font-size-small);
      color: var(--d4-color-unique);
      margin-top: .1875rem
    }

    .guide-list .guide-item .date[data-v-af6deea6] {
      font-size: var(--font-size-small);
      color: var(--color-text-grey);
      margin-top: .1875rem
    }

    @media (min-width: 1024px) {
      .guide-list .guide-item .content[data-v-af6deea6] {
        padding: 12px 16px
      }

      .guide-list .guide-item .item-img[data-v-af6deea6] {
        width: 144px;
        height: 120px
      }
    }

    .season-count[data-v-7034d807] {
      color: var(--color-text-normal);
      font-size: var(--font-size-normal);
      text-align: center;
      background:
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/d4-count-bg.png?imageView2/w/1024/format/webp/q/50*/
        var(--savepage-url-32) center center no-repeat;
      background-size: cover;
      text-shadow: 1px 1px 4px rgba(0, 0, 0, .75);
      position: relative
    }

    .season-count .season-count-content[data-v-7034d807] {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
      margin: -.375rem
    }

    .season-count .mask[data-v-7034d807] {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, .5)
    }

    .season-count .helltides[data-v-7034d807] {
      z-index: 1;
      position: relative;
      color: var(--color-primary);
      font-size: var(--font-size-normal);
      background-color: rgba(0, 0, 0, .5);
      margin: .75rem 0 0;
      padding: .75rem;
      border-radius: .375rem
    }

    .season-count .count-text[data-v-7034d807] {
      z-index: 1;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin: .1875rem
    }

    .season-count .count-text-row[data-v-7034d807] {
      margin-bottom: .25rem
    }

    .season-count[data-v-7034d807] uni-text {
      line-height: 1.2 !important
    }

    .season-count .tip[data-v-7034d807] {
      font-size: .875rem;
      margin-top: .125rem
    }

    .season-count .helltides .holder[data-v-7034d807] {
      height: 1.875rem
    }

    .season-count .helltides .container[data-v-7034d807] {
      position: absolute;
      width: 50%;
      left: 25%;
      bottom: -.3125rem
    }

    .season-count .helltides .content[data-v-7034d807] {
      color: var(--color-red)
    }

    @media (min-width: 1024px) {
      .season-count .season-count-content[data-v-7034d807] {
        flex-direction: row;
        align-items: center;
        justify-content: space-around
      }
    }

    .wiki-wrapper[data-v-a8cd5208] {
      position: relative
    }

    .wiki-wrapper .mask[data-v-a8cd5208] {
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 3.125rem;
      background: linear-gradient(to right, transparent, var(--color-bg-dark) 90%)
    }

    .wiki[data-v-a8cd5208] {
      display: flex;
      flex-direction: row;
      overflow-x: auto
    }

    .wiki .column[data-v-a8cd5208] {
      flex: 1 0 75%
    }

    .wiki .column+.column[data-v-a8cd5208] {
      margin-left: .75rem
    }

    .wiki .group+.group[data-v-a8cd5208] {
      margin-top: .75rem
    }

    .wiki .group-name[data-v-a8cd5208] {
      background: var(--color-bg-light);
      padding: .1875rem .375rem;
      margin-bottom: .5rem
    }

    .wiki .group-link[data-v-a8cd5208] {
      margin-top: .375rem;
      font-size: .875rem
    }

    .wiki .group-link-nav[data-v-a8cd5208]:hover {
      color: var(--color-primary)
    }

    @media (min-width: 1024px) {
      .wiki-wrapper .mask[data-v-a8cd5208] {
        display: none
      }

      .wiki .column[data-v-a8cd5208] {
        flex: 1
      }
    }

    .season[data-v-e0859eb7] {
      background-image: linear-gradient(90deg, black 0%, transparent 50%, black 100%),
        /*savepage-url=https://6469-diablocore-4gkv4qjs9c6a0b40-1307287922.tcb.qcloud.la/app/season-1.webp*/
        url();
      background-repeat: no-repeat;
      background-position: center center;
      background-size: 120% auto
    }

    .season .title[data-v-e0859eb7] {
      color: #333;
      text-shadow: 3px 5px 5px rgba(0, 0, 0, .5);
      text-align: center;
      font-size: var(--font-size-large)
    }

    @media (min-width: 1024px) {
      .season[data-v-e0859eb7] {
        height: 300px
      }
    }

    .container[data-v-d35f2a3c] {
      display: flex;
      flex-direction: column
    }

    .fake-status-bar[data-v-d35f2a3c] {
      height: var(--status-bar-height);
      background: rgba(0, 0, 0, .3)
    }

    .content[data-v-d35f2a3c] {
      padding: var(--padding-hoz)
    }

    .footer[data-v-d35f2a3c] {
      margin-top: 48px;
      text-align: center;
      color: var(--color-text-grey)
    }

    .title-row[data-v-d35f2a3c] {
      display: flex;
      align-items: center;
      justify-content: space-between
    }

    .more[data-v-d35f2a3c] {
      color: var(--color-text-normal);
      font-size: var(--font-size-normal);
      line-height: 1
    }

    @media (min-width: 1024px) {
      .content[data-v-d35f2a3c] {
        padding-left: 0;
        padding-right: 0
      }

      .guide-panel[data-v-d35f2a3c] {
        display: flex;
        flex-direction: row
      }

      .guide-panel .panel-item[data-v-d35f2a3c] {
        flex: 1
      }

      .guide-panel .panel-item+.panel-item[data-v-d35f2a3c] {
        margin-left: 12px
      }
    }
  </style>
 
  </style>
  <style type="text/css">
    /* Copyright 2014-present Evernote Corporation. All rights reserved. */
    .skitchToastBoxContainer {
      position: absolute;
      width: 100%;
      text-align: center;
      top: 30px;
      -webkit-user-select: none;
      -moz-user-select: none;
      pointer-events: none;
    }

    .skitchToastBox {
      width: 200px;
      height: 16px;
      padding: 12px;
      background-color: rgba(47, 55, 61, 0.95);
      border-radius: 4px;
      color: white;
      cursor: default;
      font-size: 10pt;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.32);
      font-family: 'Soleil', Helvetica, Arial, sans-serif;
      border: 2px rgba(255, 255, 255, 0.38) solid;
    }

    .lang-zh-cn .skitchToastBox {
      font-family: '微软雅黑', 'Microsoft YaHei', SimSun,
        '&#x30E1;&#x30A4;&#x30EA;&#x30AA;', Meiryo, 'MS PGothic', 'Soleil',
        Helvetica, Arial, sans-serif;
    }

    .lang-ja-jp .skitchToastBox {
      font-family: '&#x30E1;&#x30A4;&#x30EA;&#x30AA;', Meiryo, 'MS PGothic',
        '微软雅黑', 'Microsoft YaHei', SimSun, 'Soleil', Helvetica, Arial,
        sans-serif;
    }

    .skitchToast {
      padding-left: 20px;
      padding-right: 20px;
      display: inline-block;
      height: 10px;
      color: #f1f5f8;
      text-align: center;
    }

    .skitchVisible {
      /* Don't remove this class it's a hack used by the Evernote Clipper */
    }
  </style>
  <style type="text/css">
    /* Copyright 2014-present Evernote Corporation. All rights reserved. */

    @font-face {
      font-family: 'Soleil';
      font-weight: normal;
      font-style: normal;
    }
  </style>
  <style type="text/css">
    /* Copyright 2019-present Evernote Corporation. All rights reserved. */

    #en-markup-disabled {
      position: fixed;
      z-index: 9999;
      width: 100%;
      height: 100%;
      top: 0px;
      left: 0px;
      cursor: default;
      -webkit-user-select: none;
    }

    #en-markup-alert-container {
      position: absolute;
      z-index: 9999;
      width: 450px;
      left: calc(50% - 225px);
      top: calc(50% - 85px);
      background-color: white;
      box-shadow: 0 2px 7px 1px rgba(0, 0, 0, 0.35);
      -webkit-user-select: none;
    }

    

    #en-markup-alert-container .cell-2 {
      position: relative;
      float: left;
      width: 345px;
      margin-top: 29px;
      margin-bottom: 20px;
    }

    #en-markup-alert-container .cell-2 .cell-2-title {
      margin-bottom: 5px;
      padding-right: 30px;
      font-size: 12pt;
      font-family: Tahoma, Arial;
    }

    #en-markup-alert-container .cell-2 .cell-2-message {
      padding-right: 30px;
      font-size: 9.5pt;
      font-family: Tahoma, Arial;
    }

    #en-markup-alert-container .cell-3 {
      position: relative;
      width: 450px;
      height: 60px;
      float: left;
      background-color: rgb(240, 240, 240);
    }

    #en-markup-alert-container .cell-3 button {
      position: absolute;
      top: 12px;
      right: 15px;
      width: 110px;
      height: 36px;
    }

    #en-markup-alert-container .cell-3 button.alt-button {
      position: absolute;
      top: 12px;
      right: 140px;
      width: 110px;
      height: 36px;
    }
  </style>
  <title data-v-d35f2a3c="">博义娱乐</title>
  <meta data-v-d35f2a3c="" name="description" >
  <style type="text/css">
    @keyframes animate_dots {
      0% {
        opacity: 1
      }

      to {
        opacity: 0
      }
    }

    @-webkit-keyframes animate_dots {
      0% {
        opacity: 1
      }

      to {
        opacity: 0
      }
    }

    .dot0,
    .dot1 {
      animation: animate_dots .9s infinite;
      -moz-animation: animate_dots .9s infinite;
      -webkit-animation: animate_dots .9s infinite;
      -o-animation: animate_dots .9s infinite
    }

    .dot1 {
      animation-delay: .2s;
      -webkit-animation-delay: .2s
    }

    .dot2 {
      animation: animate_dots .9s infinite;
      -moz-animation: animate_dots .9s infinite;
      -webkit-animation: animate_dots .9s infinite;
      -o-animation: animate_dots .9s infinite;
      animation-delay: .4s;
      -webkit-animation-delay: .4s
    }

    .dots_item {
      display: inline-block;
      margin-right: 5px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #4886ff
    }

    .verify-icon {
      position: absolute;
      width: 100%;
      margin-top: 70px;
      text-align: center
    }

    .t-embed-loading {
      position: relative;
      width: 300px;
      height: 230px;
      background-color: #fff
    }

    .t-embed-dots {
      position: absolute;
      left: 50%;
      top: 50%;
      margin-left: -22.5px;
      margin-top: -5px
    }

    .t-embed-dot {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 5px;
      background: #4886ff
    }

    .t-mask {
      width: 100%;
      height: 100%;
      position: fixed;
      _position: absolute;
      left: 0;
      top: 0;
      background: #000;
      opacity: .5;
      filter: progid:DXImageTransform.Microsoft.Alpha(opacity=50);
      z-index: 2000000000
    }
  </style>
  <script data-savepage-type="" type="text/plain"
    data-savepage-src="chrome-extension://bpoadfkcbjbfhfodiogcnhhhpibjhbnh/image/inject.js"
    id="imt-image-inject"></script>
  <style data-id="immersive-translate-input-injected-css">
    .immersive-translate-input {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      z-index: 2147483647;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .immersive-translate-attach-loading::after {
      content: " ";

      --loading-color: #f78fb6;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      display: block;
      margin: 12px auto;
      position: relative;
      color: white;
      left: -100px;
      box-sizing: border-box;
      animation: immersiveTranslateShadowRolling 1.5s linear infinite;

      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-2000%, -50%);
      z-index: 100;
    }

    .immersive-translate-loading-spinner {
      vertical-align: middle !important;
      width: 10px !important;
      height: 10px !important;
      display: inline-block !important;
      margin: 0 4px !important;
      border: 2px rgba(221, 244, 255, 0.6) solid !important;
      border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
      border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
      border-radius: 50% !important;
      padding: 0 !important;
      -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
      animation: immersive-translate-loading-animation 0.6s infinite linear !important;
    }

    @-webkit-keyframes immersive-translate-loading-animation {
      from {
        -webkit-transform: rotate(0deg);
      }

      to {
        -webkit-transform: rotate(359deg);
      }
    }

    @keyframes immersive-translate-loading-animation {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(359deg);
      }
    }

    .immersive-translate-input-loading {
      --loading-color: #f78fb6;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      display: block;
      margin: 12px auto;
      position: relative;
      color: white;
      left: -100px;
      box-sizing: border-box;
      animation: immersiveTranslateShadowRolling 1.5s linear infinite;
    }

    @keyframes immersiveTranslateShadowRolling {
      0% {
        box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
          0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
      }

      12% {
        box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
          0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
      }

      25% {
        box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
          0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
      }

      36% {
        box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
          100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
      }

      50% {
        box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
          110px 0 var(--loading-color), 100px 0 var(--loading-color);
      }

      62% {
        box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
          120px 0 var(--loading-color), 110px 0 var(--loading-color);
      }

      75% {
        box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
          130px 0 var(--loading-color), 120px 0 var(--loading-color);
      }

      87% {
        box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
          200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
      }

      100% {
        box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
          200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
      }
    }

    .immersive-translate-toast {
      display: flex;
      position: fixed;
      z-index: 2147483647;
      left: 0;
      right: 0;
      top: 1%;
      width: fit-content;
      padding: 12px 20px;
      margin: auto;
      overflow: auto;
      background: #fef6f9;
      box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
      font-size: 15px;
      border-radius: 8px;
      color: #333;
    }

    .immersive-translate-toast-content {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .immersive-translate-toast-hidden {
      margin: 0 20px 0 72px;
      text-decoration: underline;
      cursor: pointer;
    }

    .immersive-translate-toast-close {
      color: #666666;
      font-size: 20px;
      font-weight: bold;
      padding: 0 10px;
      cursor: pointer;
    }

    @media screen and (max-width: 768px) {
      .immersive-translate-toast {
        top: 0;
        padding: 12px 0px 0 10px;
      }

      .immersive-translate-toast-content {
        flex-direction: column;
        text-align: center;
      }

      .immersive-translate-toast-hidden {
        margin: 10px auto;
      }
    }

    .immersive-translate-modal {
      display: none;
      position: fixed;
      z-index: 2147483647;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgb(0, 0, 0);
      background-color: rgba(0, 0, 0, 0.4);
      font-size: 15px;
    }

    .immersive-translate-modal-content {
      background-color: #fefefe;
      margin: 10% auto;
      padding: 40px 24px 24px;
      border: 1px solid #888;
      border-radius: 10px;
      width: 80%;
      max-width: 270px;
      font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
        "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
        "Segoe UI Symbol", "Noto Color Emoji";
      position: relative;
    }

    @media screen and (max-width: 768px) {
      .immersive-translate-modal-content {
        margin: 50% auto !important;
      }
    }

    .immersive-translate-modal .immersive-translate-modal-content-in-input {
      max-width: 500px;
    }

    .immersive-translate-modal-content-in-input .immersive-translate-modal-body {
      text-align: left;
      max-height: unset;
    }

    .immersive-translate-modal-title {
      text-align: center;
      font-size: 16px;
      font-weight: 700;
      color: #333333;
    }

    .immersive-translate-modal-body {
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      word-break: break-all;
      margin-top: 24px;
    }

    @media screen and (max-width: 768px) {
      .immersive-translate-modal-body {
        max-height: 250px;
        overflow-y: auto;
      }
    }

    .immersive-translate-close {
      color: #666666;
      position: absolute;
      right: 16px;
      top: 16px;
      font-size: 20px;
      font-weight: bold;
    }

    .immersive-translate-close:hover,
    .immersive-translate-close:focus {
      color: black;
      text-decoration: none;
      cursor: pointer;
    }

    .immersive-translate-modal-footer {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: 24px;
    }

    .immersive-translate-btn {
      width: fit-content;
      color: #fff;
      background-color: #ea4c89;
      border: none;
      font-size: 16px;
      margin: 0 8px;
      padding: 9px 30px;
      border-radius: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .immersive-translate-btn:hover {
      background-color: #f082ac;
    }

    .immersive-translate-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .immersive-translate-btn:disabled:hover {
      background-color: #ea4c89;
    }

    .immersive-translate-cancel-btn {
      /* gray color */
      background-color: rgb(89, 107, 120);
    }

    .immersive-translate-cancel-btn:hover {
      background-color: hsl(205, 20%, 32%);
    }

    .immersive-translate-action-btn {
      background-color: transparent;
      color: #ea4c89;
      border: 1px solid #ea4c89;
    }

    .immersive-translate-btn svg {
      margin-right: 5px;
    }

    .immersive-translate-link {
      cursor: pointer;
      user-select: none;
      -webkit-user-drag: none;
      text-decoration: none;
      color: #007bff;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    .immersive-translate-primary-link {
      cursor: pointer;
      user-select: none;
      -webkit-user-drag: none;
      text-decoration: none;
      color: #ea4c89;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    .immersive-translate-modal input[type="radio"] {
      margin: 0 6px;
      cursor: pointer;
    }

    .immersive-translate-modal label {
      cursor: pointer;
    }

    .immersive-translate-close-action {
      position: absolute;
      top: 2px;
      right: 0px;
      cursor: pointer;
    }

    .imt-image-status {
      background-color: rgba(0, 0, 0, 0.5) !important;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
      border-radius: 16px !important;
    }

    .imt-image-status img,
    .imt-image-status svg,
    .imt-img-loading {
      width: 28px !important;
      height: 28px !important;
      margin: 0 0 8px 0 !important;
      min-height: 28px !important;
      min-width: 28px !important;
      position: relative !important;
    }

  

    .imt-image-status span {
      color: var(--bg-2, #fff) !important;
      font-size: 14px !important;
      line-height: 14px !important;
      font-weight: 500 !important;
      font-family: "PingFang SC", Arial, sans-serif !important;
    }

    @keyframes image-loading-rotate {
      from {
        transform: rotate(360deg);
      }

      to {
        transform: rotate(0deg);
      }
    }
  </style>
  
  </style>
 

  <meta name="savepage-date" content="Sun Feb 02 2025 19:11:00 GMT+0800 (中国标准时间)">
  <meta name="savepage-state"
    content="Standard Items; Retain cross-origin frames; Merge CSS images; Remove unsaved URLs; Load lazy images in existing content; Max frame depth = 5; Max resource size = 50MB; Max resource time = 10s;">
  <meta name="savepage-version" content="33.9">
  <meta name="savepage-comments" content="">
</head>


 


